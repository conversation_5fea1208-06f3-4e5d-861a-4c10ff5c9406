/* Enhanced Modern UI Design System v2.0 */

/* Base Reset and Foundation */
*,
*::before,
*::after {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html {
  font-size: 16px;
  scroll-behavior: smooth;
  height: 100%;
}

body {
  font-family: var(--font-family);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-normal);
  line-height: var(--line-height-normal);
  color: var(--text-primary);
  background: var(--bg-dark);
  background-attachment: fixed;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeSpeed;
  overflow-x: hidden;
  min-height: 100vh;
  position: relative;
}

/* Enhanced focus styles for accessibility */
:focus-visible {
  outline: 2px solid var(--primary);
  outline-offset: 2px;
  border-radius: var(--radius);
}

/* Enhanced Typography */
h1, h2, h3, h4, h5, h6 {
  font-weight: var(--font-weight-semibold);
  line-height: 1.2;
  margin-bottom: var(--spacing-4);
  color: var(--text-primary);
}

h1 { font-size: var(--font-size-4xl); }
h2 { font-size: var(--font-size-3xl); }
h3 { font-size: var(--font-size-2xl); }
h4 { font-size: var(--font-size-xl); }
h5 { font-size: var(--font-size-lg); }
h6 { font-size: var(--font-size-base); }

p {
  margin-bottom: var(--spacing-4);
  color: var(--text-secondary);
}

/* Enhanced Links */
a {
  color: var(--primary);
  text-decoration: none;
  transition: color var(--duration-150) var(--transition-ease);
  cursor: pointer;
}

a:hover {
  color: var(--primary-light);
}

a:focus-visible {
  outline: 2px solid var(--primary);
  outline-offset: 2px;
  border-radius: var(--radius-sm);
}

/* Enhanced Buttons with Modern Effects */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-2);
  padding: var(--spacing-2-5) var(--spacing-4);
  font-family: var(--font-family);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  line-height: var(--line-height-none);
  border-radius: var(--radius-lg);
  border: none;
  cursor: pointer;
  transition: all var(--duration-200) var(--transition-ease);
  text-decoration: none;
  white-space: nowrap;
  user-select: none;
  position: relative;
  overflow: hidden;
  transform: translateZ(0);
  will-change: transform, box-shadow;
}

/* Modern button ripple effect */
.btn::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
  transform: translate(-50%, -50%);
  transition: width var(--duration-300) var(--transition-ease), height var(--duration-300) var(--transition-ease);
  z-index: 0;
}

.btn:active::before {
  width: 300px;
  height: 300px;
}

.btn > * {
  position: relative;
  z-index: 1;
}

.btn:focus-visible {
  outline: 2px solid var(--primary);
  outline-offset: 2px;
}

.btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  pointer-events: none;
  transform: none;
}

/* Enhanced Button Variants */
.btn-primary {
  background: var(--gradient-primary);
  color: white;
  box-shadow: var(--shadow-sm);
  border: 1px solid transparent;
}

.btn-primary:hover {
  background: linear-gradient(135deg, var(--primary-700) 0%, var(--primary-800) 100%);
  box-shadow: var(--shadow-colored);
  transform: translateY(-2px);
}

.btn-primary:active {
  transform: translateY(0);
  box-shadow: var(--shadow-sm);
}

.btn-secondary {
  background-color: var(--bg-card);
  color: var(--text-primary);
  border: 1px solid var(--border);
  box-shadow: var(--shadow-xs);
}

.btn-secondary:hover {
  background-color: var(--bg-card-hover);
  border-color: var(--border-light);
  box-shadow: var(--shadow-sm);
  transform: translateY(-1px);
}

.btn-accent {
  background: var(--gradient-accent);
  color: white;
  box-shadow: var(--shadow-sm);
  border: 1px solid transparent;
}

.btn-accent:hover {
  background: linear-gradient(135deg, var(--accent-600) 0%, var(--warning) 100%);
  box-shadow: var(--shadow-md);
  transform: translateY(-2px);
}

.btn-ghost {
  background-color: transparent;
  color: var(--text-secondary);
  border: 1px solid transparent;
}

.btn-ghost:hover {
  background-color: var(--bg-card);
  color: var(--text-primary);
  border-color: var(--border);
}

.btn-glass {
  background: var(--glass-bg);
  color: var(--text-primary);
  border: 1px solid var(--glass-border);
  backdrop-filter: var(--glass-backdrop);
  -webkit-backdrop-filter: var(--glass-backdrop);
  box-shadow: var(--glass-shadow);
}

.btn-glass:hover {
  background: rgba(30, 41, 59, 0.9);
  border-color: rgba(255, 255, 255, 0.2);
  transform: translateY(-1px);
}

/* Enhanced Button Sizes */
.btn-xs {
  padding: var(--spacing-1) var(--spacing-2);
  font-size: var(--font-size-xs);
  border-radius: var(--radius-md);
}

.btn-sm {
  padding: var(--spacing-1-5) var(--spacing-3);
  font-size: var(--font-size-xs);
  border-radius: var(--radius-md);
}

.btn-lg {
  padding: var(--spacing-3) var(--spacing-6);
  font-size: var(--font-size-base);
  border-radius: var(--radius-xl);
}

.btn-xl {
  padding: var(--spacing-4) var(--spacing-8);
  font-size: var(--font-size-lg);
  border-radius: var(--radius-xl);
}

/* Enhanced Forms */
.form-group {
  margin-bottom: var(--spacing-6);
}

.form-label {
  display: block;
  margin-bottom: var(--spacing-2);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--text-primary);
}

.form-control {
  display: block;
  width: 100%;
  padding: var(--spacing-3) var(--spacing-4);
  font-size: var(--font-size-base);
  line-height: 1.5;
  color: var(--text-primary);
  background-color: var(--bg-card);
  border: 1px solid var(--border);
  border-radius: var(--radius-lg);
  transition: all var(--duration-150) var(--transition-ease);
}

.form-control:focus {
  border-color: var(--primary);
  outline: none;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-control::placeholder {
  color: var(--text-muted);
}

/* Enhanced Search Form */
.search-form {
  display: flex;
  gap: var(--spacing-2);
  width: 100%;
  max-width: var(--content-lg);
  margin: 0 auto;
}

.search-select {
  flex-shrink: 0;
  min-width: 120px;
  background-color: var(--bg-card);
  color: var(--text-primary);
  border: 1px solid var(--border);
  border-radius: var(--radius-lg);
  padding: var(--spacing-3) var(--spacing-4);
  font-size: var(--font-size-sm);
  transition: all var(--duration-150) var(--transition-ease);
}

.search-select:focus {
  border-color: var(--primary);
  outline: none;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.search-input {
  flex: 1;
  background-color: var(--bg-card);
  color: var(--text-primary);
  border: 1px solid var(--border);
  border-radius: var(--radius-lg);
  padding: var(--spacing-3) var(--spacing-4);
  font-size: var(--font-size-base);
  transition: all var(--duration-150) var(--transition-ease);
}

.search-input:focus {
  border-color: var(--primary);
  outline: none;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.search-input::placeholder {
  color: var(--text-muted);
}

.search-button {
  flex-shrink: 0;
  background-color: var(--primary);
  color: white;
  border: none;
  border-radius: var(--radius-lg);
  padding: var(--spacing-3) var(--spacing-6);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  cursor: pointer;
  transition: all var(--duration-150) var(--transition-ease);
  box-shadow: var(--shadow-sm);
}

.search-button:hover {
  background-color: var(--primary-dark);
  box-shadow: var(--shadow-md);
  transform: translateY(-1px);
}

.search-button:focus-visible {
  outline: 2px solid var(--primary-light);
  outline-offset: 2px;
}

/* Enhanced Container */
.container {
  width: 100%;
  max-width: var(--content-7xl);
  margin: 0 auto;
  padding: 0 var(--spacing-4);
}

/* Enhanced Header */
.site-header {
  background-color: var(--bg-card);
  backdrop-filter: blur(12px);
  -webkit-backdrop-filter: blur(12px);
  border-bottom: 1px solid var(--border);
  position: sticky;
  top: 0;
  z-index: var(--z-sticky);
  transition: all var(--duration-300) var(--transition-ease);
}

.header-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-4) 0;
  gap: var(--spacing-6);
}

.site-logo {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-bold);
  color: var(--text-primary);
  transition: all var(--duration-150) var(--transition-ease);
}

.site-logo a {
  color: inherit;
  text-decoration: none;
}

.site-logo:hover {
  color: var(--primary-light);
}

/* Enhanced Navigation */
.main-nav {
  display: flex;
  align-items: center;
  gap: var(--spacing-6);
}

.nav-link {
  color: var(--text-secondary);
  font-weight: var(--font-weight-medium);
  padding: var(--spacing-2) var(--spacing-3);
  border-radius: var(--radius-md);
  transition: all var(--duration-150) var(--transition-ease);
  text-decoration: none;
}

.nav-link:hover {
  color: var(--text-primary);
  background-color: var(--bg-card-hover);
}

.nav-link.active {
  color: var(--primary);
  background-color: rgba(59, 130, 246, 0.1);
}

/* Enhanced Cards */
.card {
  background-color: var(--bg-card);
  border-radius: var(--radius-xl);
  border: 1px solid var(--border);
  overflow: hidden;
  transition: all var(--duration-200) var(--transition-ease);
  box-shadow: var(--shadow-sm);
}

.card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
  border-color: var(--border-light);
}

.card-header {
  padding: var(--spacing-6);
  border-bottom: 1px solid var(--border);
  background-color: var(--bg-card);
}

.card-body {
  padding: var(--spacing-6);
}

.card-footer {
  padding: var(--spacing-6);
  border-top: 1px solid var(--border);
  background-color: var(--bg-card);
}

/* Enhanced Media Cards - Override legacy styles */
.card-grid,
.card-grid.vod-list {
  display: grid !important;
  grid-template-columns: repeat(auto-fill, minmax(160px, 1fr)) !important;
  gap: var(--spacing-6) !important;
  margin-bottom: var(--spacing-8) !important;
  contain: layout style paint;
  background-color: transparent !important;
  flex-wrap: unset !important;
  justify-content: unset !important;
}

.media-card,
.card-grid .media-card,
.vod-list .media-card,
.vod-list .vod-item.media-card {
  position: relative !important;
  overflow: hidden !important;
  border-radius: var(--radius-xl) !important;
  background-color: var(--bg-card) !important;
  transition: all var(--duration-200) var(--transition-ease) !important;
  transform: translateZ(0) !important;
  box-shadow: var(--shadow-sm) !important;
  border: 1px solid transparent !important;
  width: auto !important;
  margin-bottom: 0 !important;
  display: block !important;
  flex-direction: unset !important;
}

.media-card:hover,
.card-grid .media-card:hover,
.vod-list .media-card:hover,
.vod-list .vod-item.media-card:hover {
  transform: translateY(-6px) scale(1.03) !important;
  box-shadow: var(--shadow-2xl) !important;
  border-color: var(--primary) !important;
  z-index: 2 !important;
}

.media-card:active,
.card-grid .media-card:active,
.vod-list .media-card:active,
.vod-list .vod-item.media-card:active {
  transform: translateY(-2px) scale(1.01) !important;
  transition-duration: var(--duration-100) !important;
}

.media-card:focus-within {
  outline: 2px solid var(--primary);
  outline-offset: 2px;
}

.media-poster,
.card-grid .media-poster,
.vod-list .media-poster,
.vod-list .vod-poster.media-poster {
  width: 100% !important;
  aspect-ratio: 2/3 !important;
  position: relative !important;
  overflow: hidden !important;
  background: linear-gradient(135deg, var(--bg-card) 0%, var(--bg-card-hover) 100%) !important;
  background-color: var(--bg-card) !important;
  background-position: unset !important;
  border-radius: var(--radius-lg) var(--radius-lg) 0 0 !important;
  height: auto !important;
  padding-bottom: 0 !important;
}

/* Fallback for browsers that don't support aspect-ratio */
@supports not (aspect-ratio: 2/3) {
  .media-poster,
  .card-grid .media-poster,
  .vod-list .media-poster,
  .vod-list .vod-poster.media-poster {
    height: 0 !important;
    padding-bottom: 150% !important;
  }
}

.media-poster img,
.card-grid .media-poster img,
.vod-list .media-poster img,
.vod-list .vod-poster.media-poster img,
.vod-list .vod-poster > img {
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  right: unset !important;
  width: 100% !important;
  height: 100% !important;
  object-fit: cover !important;
  transition: transform var(--duration-300) var(--transition-ease) !important;
}

.media-card:hover .media-poster img {
  transform: scale(1.05);
}

.media-badge,
.card-grid .media-badge,
.vod-list .media-badge,
.vod-list .vod-badge.media-badge,
.vod-list .vod-poster .vod-badge {
  position: absolute !important;
  top: var(--spacing-2) !important;
  right: var(--spacing-2) !important;
  background: linear-gradient(135deg, var(--accent) 0%, var(--accent-dark) 100%) !important;
  color: white !important;
  padding: var(--spacing-1) var(--spacing-2) !important;
  border-radius: var(--radius-full) !important;
  font-size: var(--font-size-xs) !important;
  font-weight: var(--font-weight-semibold) !important;
  box-shadow: var(--shadow-md) !important;
  backdrop-filter: blur(8px) !important;
  -webkit-backdrop-filter: blur(8px) !important;
  border: none !important;
  filter: none !important;
  letter-spacing: normal !important;
  opacity: 1 !important;
}

.media-info,
.card-grid .media-info,
.vod-list .media-info,
.vod-list .vod-memo.media-info,
.vod-list .vod-poster .vod-memo {
  position: absolute !important;
  bottom: var(--spacing-2) !important;
  left: var(--spacing-2) !important;
  right: var(--spacing-2) !important;
  background: rgba(0, 0, 0, 0.8) !important;
  color: white !important;
  padding: var(--spacing-1-5) var(--spacing-2) !important;
  border-radius: var(--radius-md) !important;
  font-size: var(--font-size-xs) !important;
  font-weight: var(--font-weight-medium) !important;
  backdrop-filter: blur(8px) !important;
  -webkit-backdrop-filter: blur(8px) !important;
  opacity: 0 !important;
  transform: translateY(100%) !important;
  transition: all var(--duration-200) var(--transition-ease) !important;
  max-width: unset !important;
  white-space: normal !important;
  overflow: visible !important;
  text-overflow: unset !important;
}

.media-card:hover .media-info,
.card-grid .media-card:hover .media-info,
.vod-list .media-card:hover .media-info,
.vod-list .vod-item.media-card:hover .vod-memo {
  opacity: 1 !important;
  transform: translateY(0) !important;
}

.media-title,
.card-grid .media-title,
.vod-list .media-title,
.vod-list .vod-title.media-title {
  padding: var(--spacing-3) var(--spacing-4) !important;
  font-size: var(--font-size-sm) !important;
  font-weight: var(--font-weight-medium) !important;
  color: var(--text-primary) !important;
  line-height: 1.4 !important;
  display: -webkit-box !important;
  -webkit-line-clamp: 2 !important;
  -webkit-box-orient: vertical !important;
  overflow: hidden !important;
  text-overflow: ellipsis !important;
  text-align: left !important;
  margin: 0 !important;
  letter-spacing: normal !important;
  text-shadow: none !important;
  white-space: normal !important;
  transition: color var(--duration-150) var(--transition-ease) !important;
}

.media-card:hover .media-title,
.card-grid .media-card:hover .media-title,
.vod-list .media-card:hover .media-title,
.vod-list .vod-item.media-card:hover .vod-title {
  color: var(--primary) !important;
}

/* Enhanced Hero Section with Modern Effects */
.hero {
  text-align: center;
  padding: var(--spacing-20) 0 var(--spacing-16);
  background: linear-gradient(135deg, var(--bg-dark) 0%, var(--bg-card) 50%, var(--bg-dark) 100%);
  position: relative;
  overflow: hidden;
}

.hero::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 30% 20%, rgba(37, 99, 235, 0.15) 0%, transparent 50%),
    radial-gradient(circle at 70% 80%, rgba(245, 158, 11, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 50% 50%, rgba(5, 150, 105, 0.05) 0%, transparent 70%);
  pointer-events: none;
  animation: heroGlow 8s ease-in-out infinite alternate;
}

@keyframes heroGlow {
  0% { opacity: 0.8; }
  100% { opacity: 1; }
}

.hero::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.03)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
  pointer-events: none;
  opacity: 0.5;
}

.hero > * {
  position: relative;
  z-index: 1;
}

.hero-title {
  font-family: var(--font-family-display);
  font-size: var(--font-size-5xl);
  font-weight: var(--font-weight-bold);
  line-height: var(--line-height-tight);
  margin-bottom: var(--spacing-4);
  background: linear-gradient(135deg, var(--text-primary) 0%, var(--primary-300) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  letter-spacing: -0.05em;
}

.hero-subtitle {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-normal);
  color: var(--text-secondary);
  margin-bottom: var(--spacing-8);
  line-height: var(--line-height-relaxed);
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
  background-clip: text;
  position: relative;
  z-index: 1;
}

.hero-subtitle {
  font-size: var(--font-size-xl);
  color: var(--text-secondary);
  margin-bottom: var(--spacing-8);
  max-width: var(--content-2xl);
  margin-left: auto;
  margin-right: auto;
  position: relative;
  z-index: 1;
}

/* Enhanced Tabs */
.tabs {
  margin-bottom: var(--spacing-8);
}

.tab-buttons {
  display: flex;
  gap: var(--spacing-2);
  margin-bottom: var(--spacing-6);
  border-bottom: 1px solid var(--border);
  padding-bottom: var(--spacing-2);
  overflow-x: auto;
  scrollbar-width: none;
  -ms-overflow-style: none;
}

.tab-buttons::-webkit-scrollbar {
  display: none;
}

.tab-button {
  position: relative;
  padding: var(--spacing-3) var(--spacing-6);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  color: var(--text-secondary);
  background: none;
  border: none;
  border-radius: var(--radius-lg) var(--radius-lg) 0 0;
  cursor: pointer;
  transition: all var(--duration-200) var(--transition-ease);
  white-space: nowrap;
  flex-shrink: 0;
}

.tab-button::after {
  content: '';
  position: absolute;
  bottom: -3px;
  left: 0;
  right: 0;
  height: 3px;
  background-color: var(--primary);
  border-radius: var(--radius-full);
  transform: scaleX(0);
  transition: transform var(--duration-200) var(--transition-ease);
}

.tab-button:hover {
  color: var(--text-primary);
  background-color: var(--bg-card-hover);
}

.tab-button.active {
  color: var(--primary);
  background-color: var(--bg-card);
}

.tab-button.active::after {
  transform: scaleX(1);
}

.tab-content {
  display: none;
  animation: fadeIn var(--duration-300) var(--transition-ease);
}

.tab-content.active {
  display: block;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Enhanced Bottom Navigation */
.bottom-nav {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: var(--bg-card);
  backdrop-filter: blur(12px);
  -webkit-backdrop-filter: blur(12px);
  border-top: 1px solid var(--border);
  z-index: var(--z-sticky);
  display: none;
  padding: var(--spacing-2) 0;
  box-shadow: var(--shadow-lg);
}

.bottom-nav-buttons {
  display: flex;
  justify-content: space-around;
  align-items: center;
  max-width: var(--content-lg);
  margin: 0 auto;
  padding: 0 var(--spacing-4);
}

.bottom-nav-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-1);
  padding: var(--spacing-2);
  color: var(--text-tertiary);
  text-decoration: none;
  border-radius: var(--radius-lg);
  transition: all var(--duration-150) var(--transition-ease);
  min-width: 60px;
  position: relative;
}

.bottom-nav-btn:hover,
.bottom-nav-btn.active {
  color: var(--primary);
  background-color: rgba(59, 130, 246, 0.1);
}

.bottom-nav-btn-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
}

.bottom-nav-btn-label {
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
  line-height: 1;
}

/* Enhanced Dropdowns and Modals */
.dropdown-content,
.modal-content {
  background-color: var(--bg-card);
  border: 1px solid var(--border);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-xl);
  backdrop-filter: blur(12px);
  -webkit-backdrop-filter: blur(12px);
  overflow: hidden;
  z-index: var(--z-dropdown);
}

.dropdown-header,
.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-6);
  border-bottom: 1px solid var(--border);
  background-color: var(--bg-card);
}

.dropdown-title,
.modal-title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  margin: 0;
}

.dropdown-close,
.modal-close {
  background: none;
  border: none;
  color: var(--text-tertiary);
  font-size: var(--font-size-xl);
  cursor: pointer;
  padding: var(--spacing-2);
  border-radius: var(--radius-md);
  transition: all var(--duration-150) var(--transition-ease);
  line-height: 1;
}

.dropdown-close:hover,
.modal-close:hover {
  color: var(--text-primary);
  background-color: var(--bg-card-hover);
}

.dropdown-body,
.modal-body {
  padding: var(--spacing-6);
  max-height: 60vh;
  overflow-y: auto;
  scrollbar-width: thin;
  scrollbar-color: var(--border) transparent;
}

.dropdown-body::-webkit-scrollbar,
.modal-body::-webkit-scrollbar {
  width: 6px;
}

.dropdown-body::-webkit-scrollbar-track,
.modal-body::-webkit-scrollbar-track {
  background: transparent;
}

.dropdown-body::-webkit-scrollbar-thumb,
.modal-body::-webkit-scrollbar-thumb {
  background-color: var(--border);
  border-radius: var(--radius-full);
}

.dropdown-body::-webkit-scrollbar-thumb:hover,
.modal-body::-webkit-scrollbar-thumb:hover {
  background-color: var(--border-light);
}

/* Enhanced Lists */
.list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.list-item {
  display: flex;
  align-items: center;
  padding: var(--spacing-3) var(--spacing-4);
  border-radius: var(--radius-lg);
  transition: all var(--duration-150) var(--transition-ease);
  margin-bottom: var(--spacing-1);
}

.list-item:hover {
  background-color: var(--bg-card-hover);
}

.list-item a {
  color: var(--text-primary);
  text-decoration: none;
  flex: 1;
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
}

.list-item a:hover {
  color: var(--primary);
}

/* Enhanced Toggle Components */
.toggle-container {
  display: flex;
  background-color: var(--bg-card);
  border: 1px solid var(--border);
  border-radius: var(--radius-full);
  padding: var(--spacing-1);
  position: relative;
  width: fit-content;
}

.toggle-button {
  padding: var(--spacing-2) var(--spacing-4);
  font-weight: var(--font-weight-medium);
  color: var(--text-tertiary);
  background: none;
  border: none;
  border-radius: var(--radius-full);
  cursor: pointer;
  transition: all var(--duration-200) var(--transition-ease);
  position: relative;
  z-index: 1;
  min-width: 80px;
  text-align: center;
}

.toggle-button.active {
  color: white;
}

.toggle-button:not(.active):hover {
  color: var(--text-secondary);
}

.toggle-slider {
  position: absolute;
  top: var(--spacing-1);
  left: var(--spacing-1);
  height: calc(100% - var(--spacing-2));
  width: calc(50% - var(--spacing-1));
  background: linear-gradient(135deg, var(--primary) 0%, var(--primary-dark) 100%);
  border-radius: var(--radius-full);
  transition: transform var(--duration-300) var(--transition-spring);
  z-index: 0;
  box-shadow: var(--shadow-sm);
}

.toggle-slider.right {
  transform: translateX(100%);
}

/* Enhanced Loading States */
.skeleton {
  background: linear-gradient(90deg, var(--bg-card) 25%, var(--bg-card-hover) 37%, var(--bg-card) 63%);
  background-size: 400% 100%;
  animation: skeleton-shimmer 1.5s infinite linear;
  border-radius: var(--radius-lg);
}

@keyframes skeleton-shimmer {
  0% {
    background-position: 100% 0;
  }
  100% {
    background-position: 0 0;
  }
}

.loading-spinner {
  display: inline-block;
  width: 20px;
  height: 20px;
  border: 2px solid var(--border);
  border-top: 2px solid var(--primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* Theme Toggle Component */
.theme-toggle {
  position: relative;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 44px;
  height: 44px;
  background: var(--bg-card);
  border: 1px solid var(--border);
  border-radius: var(--radius-full);
  cursor: pointer;
  transition: all var(--duration-200) var(--transition-ease);
  box-shadow: var(--shadow-sm);
}

.theme-toggle:hover {
  background: var(--bg-card-hover);
  border-color: var(--border-light);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.theme-toggle svg {
  width: 20px;
  height: 20px;
  color: var(--text-secondary);
  transition: all var(--duration-200) var(--transition-ease);
}

.theme-toggle:hover svg {
  color: var(--text-primary);
  transform: rotate(180deg);
}

/* Enhanced Utility Classes */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.flex { display: flex; }
.flex-col { flex-direction: column; }
.items-center { align-items: center; }
.justify-center { justify-content: center; }
.justify-between { justify-content: space-between; }
.justify-around { justify-content: space-around; }

.w-full { width: 100%; }
.h-full { height: 100%; }

.hidden { display: none; }
.block { display: block; }
.inline-block { display: inline-block; }

.relative { position: relative; }
.absolute { position: absolute; }
.fixed { position: fixed; }
.sticky { position: sticky; }

.overflow-hidden { overflow: hidden; }
.overflow-auto { overflow: auto; }

.pointer-events-none { pointer-events: none; }
.cursor-pointer { cursor: pointer; }

/* Responsive Design */
/* Mobile First Approach */

/* Small devices (landscape phones, 576px and up) */
@media (min-width: 576px) {
  .card-grid {
    grid-template-columns: repeat(auto-fill, minmax(140px, 1fr));
  }

  .hero-title {
    font-size: var(--font-size-6xl);
  }
}

/* Medium devices (tablets, 768px and up) */
@media (min-width: 768px) {
  .bottom-nav {
    display: none;
  }

  .card-grid {
    grid-template-columns: repeat(auto-fill, minmax(160px, 1fr));
    gap: var(--spacing-8);
  }

  .hero {
    padding: var(--spacing-20) 0 var(--spacing-16);
  }

  .container {
    padding: 0 var(--spacing-6);
  }

  .search-form {
    max-width: var(--content-xl);
  }

  .tab-buttons {
    gap: var(--spacing-4);
  }
}

/* Large devices (desktops, 992px and up) */
@media (min-width: 992px) {
  .card-grid {
    grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
  }

  .hero-title {
    font-size: var(--font-size-6xl);
  }

  .hero-subtitle {
    font-size: var(--font-size-2xl);
  }
}

/* Extra large devices (large desktops, 1200px and up) */
@media (min-width: 1200px) {
  .card-grid {
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  }

  .container {
    padding: 0 var(--spacing-8);
  }
}

/* Mobile-specific styles */
@media (max-width: 767px) {
  .bottom-nav {
    display: block;
  }

  body {
    padding-bottom: 80px; /* Account for bottom navigation */
  }

  .hero {
    padding: var(--spacing-12) 0 var(--spacing-8);
  }

  .hero-title {
    font-size: var(--font-size-3xl);
  }

  .hero-subtitle {
    font-size: var(--font-size-lg);
  }

  .search-form {
    flex-direction: column;
    gap: var(--spacing-3);
  }

  .search-select {
    min-width: auto;
  }

  .card-grid {
    grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
    gap: var(--spacing-4);
  }

  .media-title {
    font-size: var(--font-size-xs);
    padding: var(--spacing-2) var(--spacing-3);
  }

  .tab-buttons {
    gap: var(--spacing-1);
    padding: 0 var(--spacing-2);
  }

  .tab-button {
    padding: var(--spacing-2) var(--spacing-4);
    font-size: var(--font-size-sm);
  }

  .btn {
    width: 100%;
    justify-content: center;
  }

  .btn + .btn {
    margin-top: var(--spacing-2);
  }

  .header-container {
    flex-wrap: wrap;
    gap: var(--spacing-4);
  }

  .main-nav {
    gap: var(--spacing-4);
  }

  .site-logo {
    font-size: var(--font-size-lg);
  }

  /* Mobile dropdown adjustments */
  .dropdown-content,
  .modal-content {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border-radius: 0;
    max-height: 100vh;
  }

  .dropdown-body,
  .modal-body {
    max-height: calc(100vh - 120px);
  }
}

/* Accessibility and reduced motion */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }

  .media-card:hover {
    transform: none !important;
  }

  .skeleton {
    animation: none !important;
  }

  .loading-spinner {
    animation: none !important;
  }
}

/* High contrast mode */
@media (prefers-contrast: high) {
  .media-card,
  .card {
    border: 2px solid currentColor;
  }

  .btn {
    border: 2px solid currentColor;
  }

  a {
    text-decoration: underline;
  }
}

/* Dark mode adjustments (if system preference changes) */
@media (prefers-color-scheme: light) {
  /* Keep dark theme as default, but could add light theme variables here if needed */
}

/* Enhanced Episode List Styles */
.episodes-grid,
.vod-episodes .episodes-grid {
  display: grid !important;
  grid-template-columns: repeat(auto-fill, minmax(100px, 1fr)) !important;
  gap: var(--spacing-3) !important;
  max-height: 400px !important;
  overflow-y: auto !important;
  padding: var(--spacing-4) !important;
  background-color: var(--bg-card) !important;
  border-radius: var(--radius-xl) !important;
  border: 1px solid var(--border) !important;
  scrollbar-width: thin !important;
  scrollbar-color: var(--border) transparent !important;
  -webkit-overflow-scrolling: touch !important;
}

/* Desktop scrollbar styling */
@media (min-width: 769px) {
  .episodes-grid::-webkit-scrollbar {
    width: 4px !important;
  }

  .episodes-grid::-webkit-scrollbar-track {
    background: transparent !important;
  }

  .episodes-grid::-webkit-scrollbar-thumb {
    background-color: var(--border) !important;
    border-radius: var(--radius-full) !important;
    opacity: 0.5 !important;
  }

  .episodes-grid::-webkit-scrollbar-thumb:hover {
    background-color: var(--border-light) !important;
    opacity: 0.8 !important;
  }
}

/* Mobile scrollbar styling */
@media (max-width: 768px) {
  .episodes-grid::-webkit-scrollbar {
    width: 2px !important;
  }

  .episodes-grid::-webkit-scrollbar-track {
    background: transparent !important;
  }

  .episodes-grid::-webkit-scrollbar-thumb {
    background-color: var(--border) !important;
    border-radius: var(--radius-full) !important;
  }

  .episodes-grid::-webkit-scrollbar-thumb:hover {
    background-color: var(--border-light) !important;
  }
}

.episode-button,
.vod-episode,
.episodes-grid .episode-button,
.episodes-grid .vod-episode {
  background-color: var(--bg-dark) !important;
  color: var(--text-secondary) !important;
  border: 1px solid var(--border) !important;
  border-radius: var(--radius-lg) !important;
  padding: var(--spacing-3) var(--spacing-2) !important;
  text-align: center !important;
  cursor: pointer !important;
  transition: all var(--duration-200) var(--transition-ease) !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  min-height: 44px !important;
  text-overflow: ellipsis !important;
  white-space: nowrap !important;
  overflow: hidden !important;
  font-size: var(--font-size-sm) !important;
  font-weight: var(--font-weight-medium) !important;
  position: relative !important;
  text-decoration: none !important;
}

.episode-button:hover,
.vod-episode:hover,
.episodes-grid .episode-button:hover,
.episodes-grid .vod-episode:hover {
  background-color: var(--primary) !important;
  color: white !important;
  border-color: var(--primary) !important;
  transform: translateY(-2px) !important;
  box-shadow: var(--shadow-md) !important;
}

.episode-button.active,
.vod-episode.active,
.vod-episode-curr,
.episodes-grid .episode-button.active,
.episodes-grid .vod-episode.active,
.episodes-grid .vod-episode-curr {
  background-color: var(--primary) !important;
  color: white !important;
  border-color: var(--primary) !important;
  font-weight: var(--font-weight-semibold) !important;
  box-shadow: var(--shadow-glow) !important;
}

.episode-button a,
.vod-episode a,
.vod-episode-play {
  display: block !important;
  width: 100% !important;
  height: 100% !important;
  color: inherit !important;
  text-decoration: none !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  padding: 0 !important;
  margin: 0 !important;
}

/* Enhanced Tab System for Episodes */
.tab-buttons,
.tabs-head {
  display: flex !important;
  gap: var(--spacing-2) !important;
  margin-bottom: var(--spacing-6) !important;
  border-bottom: 1px solid var(--border) !important;
  padding-bottom: var(--spacing-2) !important;
  flex-wrap: wrap !important;
  background-color: transparent !important;
  border-radius: 0 !important;
  padding: var(--spacing-2) 0 !important;
  box-shadow: none !important;
}

.tab-button,
.tabs-head-btn {
  position: relative !important;
  padding: var(--spacing-3) var(--spacing-6) !important;
  font-size: var(--font-size-base) !important;
  font-weight: var(--font-weight-medium) !important;
  color: var(--text-secondary) !important;
  background: var(--bg-card) !important;
  border: 1px solid var(--border) !important;
  border-radius: var(--radius-lg) var(--radius-lg) 0 0 !important;
  cursor: pointer !important;
  transition: all var(--duration-200) var(--transition-ease) !important;
  white-space: nowrap !important;
  flex-shrink: 0 !important;
  text-decoration: none !important;
  display: inline-flex !important;
  align-items: center !important;
  gap: var(--spacing-2) !important;
}

.tab-button::after,
.tabs-head-btn::after {
  content: '' !important;
  position: absolute !important;
  bottom: -3px !important;
  left: 0 !important;
  right: 0 !important;
  height: 3px !important;
  background-color: var(--primary) !important;
  border-radius: var(--radius-full) !important;
  transform: scaleX(0) !important;
  transition: transform var(--duration-200) var(--transition-ease) !important;
}

.tab-button:hover,
.tabs-head-btn:hover {
  color: var(--text-primary) !important;
  background-color: var(--bg-card-hover) !important;
  border-color: var(--border-light) !important;
}

.tab-button.active,
.tab-button.tabs-curr,
.tabs-head-btn.active,
.tabs-head-btn.tabs-curr {
  color: var(--primary) !important;
  background-color: var(--bg-card) !important;
  border-color: var(--primary) !important;
  font-weight: var(--font-weight-semibold) !important;
}

.tab-button.active::after,
.tab-button.tabs-curr::after,
.tabs-head-btn.active::after,
.tabs-head-btn.tabs-curr::after {
  transform: scaleX(1) !important;
}

.tab-button sup,
.tabs-head-btn sup {
  color: var(--primary-light) !important;
  font-size: var(--font-size-xs) !important;
  font-weight: var(--font-weight-bold) !important;
  margin-left: var(--spacing-1) !important;
}

/* Tab Content */
.tab-content,
.vod-episodes {
  display: none !important;
  animation: fadeIn var(--duration-300) var(--transition-ease) !important;
}

.tab-content.active,
.tab-content.tabs-curr,
.vod-episodes.active,
.vod-episodes.tabs-curr {
  display: block !important;
}

/* Responsive Episode Styles */
@media (max-width: 768px) {
  .episodes-grid,
  .vod-episodes .episodes-grid {
    grid-template-columns: repeat(auto-fill, minmax(80px, 1fr)) !important;
    gap: var(--spacing-2) !important;
    max-height: 300px !important;
    padding: var(--spacing-3) !important;
  }

  .episode-button,
  .vod-episode,
  .episodes-grid .episode-button,
  .episodes-grid .vod-episode {
    min-height: 36px !important;
    padding: var(--spacing-2) var(--spacing-1) !important;
    font-size: var(--font-size-xs) !important;
  }

  .tab-button,
  .tabs-head-btn {
    padding: var(--spacing-2) var(--spacing-4) !important;
    font-size: var(--font-size-sm) !important;
  }
}

@media (min-width: 1200px) {
  .episodes-grid,
  .vod-episodes .episodes-grid {
    max-height: 500px !important;
    grid-template-columns: repeat(auto-fill, minmax(120px, 1fr)) !important;
  }

  .episode-button,
  .vod-episode,
  .episodes-grid .episode-button,
  .episodes-grid .vod-episode {
    min-height: 48px !important;
    padding: var(--spacing-3) var(--spacing-4) !important;
  }
}

/* Enhanced Mobile Categories Fix */
@media (max-width: 768px) {
  /* Ensure categories dropdown works properly on mobile */
  #categories-dropdown {
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    right: 0 !important;
    bottom: 0 !important;
    width: 100% !important;
    height: 100vh !important;
    z-index: 9999 !important;
    background-color: var(--bg-dark) !important;
    transform: translateY(100%) !important;
    transition: transform 0.3s cubic-bezier(0.16, 1, 0.3, 1) !important;
  }

  #categories-dropdown.show,
  #categories-dropdown.active {
    transform: translateY(0) !important;
    visibility: visible !important;
    opacity: 1 !important;
  }

  .categories-header {
    padding: var(--spacing-6) var(--spacing-4) var(--spacing-4) !important;
    border-bottom: 1px solid var(--border) !important;
    background-color: var(--bg-card) !important;
  }

  .categories-body {
    padding: var(--spacing-4) !important;
    max-height: calc(100vh - 120px) !important;
    overflow-y: auto !important;
    -webkit-overflow-scrolling: touch !important;
  }

  .categories-close {
    position: absolute !important;
    top: var(--spacing-4) !important;
    right: var(--spacing-4) !important;
    width: 44px !important;
    height: 44px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    background-color: var(--bg-dark) !important;
    border: 1px solid var(--border) !important;
    border-radius: var(--radius-full) !important;
    color: var(--text-secondary) !important;
    font-size: 1.5rem !important;
    cursor: pointer !important;
    transition: all var(--duration-200) var(--transition-ease) !important;
  }

  .categories-close:hover {
    background-color: var(--bg-card-hover) !important;
    color: var(--text-primary) !important;
  }
}

/* Print styles */
@media print {
  .bottom-nav,
  .site-header,
  .btn,
  .search-form {
    display: none !important;
  }

  .media-card:hover {
    transform: none !important;
    box-shadow: none !important;
  }

  .card-grid {
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    gap: var(--spacing-4);
  }

  body {
    background: white !important;
    color: black !important;
  }

  .media-card,
  .card {
    background: white !important;
    border: 1px solid #ccc !important;
  }
}
