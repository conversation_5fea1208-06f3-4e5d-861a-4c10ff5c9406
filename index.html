{| extend("base.html") |}

{| block top_bar_search_form |}{| end block top_bar_search_form |}

{| block content |}
{[
local total = func.vod.count(0)
local amount = total
if total > 9999 then
amount = string.format('%.1f 万', total / 10000)
end
]}

<!-- Enhanced Hero Section -->
<section class="hero">
    <div class="container">
        <h1 class="hero-title">发现精彩影视内容</h1>
        <p class="hero-subtitle">探索最新、最热门的电影和电视剧</p>

        <form class="search-form" action="/vod/search/" method="GET">
            <select name="field" class="search-select">
                <option value="title">搜标题</option>
                <option value="tag">搜标签</option>
                <option value="staff">搜人员</option>
            </select>
            <input name="kw" id="idx-search-input" class="search-input" placeholder="输入关键词" autocomplete="off">
            <button type="submit" class="search-button btn btn-primary">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <circle cx="11" cy="11" r="8"></circle>
                    <line x1="21" y1="21" x2="16.65" y2="16.65"></line>
                </svg>
                搜索
            </button>
        </form>

        <div class="updated text-center" style="color: var(--text-tertiary); margin-top: var(--spacing-6);">
            <span class="inline-flex items-center gap-2">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <path d="M12 2v20M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6"></path>
                </svg>
                最近更新：{= func.vod.count(86400000) =} &nbsp; 全部：<span title="{= total =}">{= amount =}</span>
            </span>
        </div>
    </div>
</section>

<!-- Enhanced Tabs Section -->
<section class="tabs container">
    <!-- Tab buttons -->
    <div class="tab-buttons">
        <button class="tab-button active" data-tab="recent" role="tab" aria-selected="true" aria-controls="recent-tab">
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="M12 2v20M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6"></path>
            </svg>
            最近更新
        </button>
        <button class="tab-button" data-tab="douban" role="tab" aria-selected="false" aria-controls="douban-tab">
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <polygon points="12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2"></polygon>
            </svg>
            豆瓣推荐
        </button>
    </div>

    <!-- Tab content -->
    <div class="tab-content-container">
        <!-- Recent updates tab (visible by default) -->
        <div class="tab-content active" id="recent-tab" role="tabpanel" aria-labelledby="recent-tab-button">
            {| include("vod/incl_list.html", { vod_list = func.vod.last_updated() }) |}
        </div>

        <!-- Douban recommendations tab (hidden by default) -->
        <div class="tab-content" id="douban-tab" role="tabpanel" aria-labelledby="douban-tab-button">
            <div class="flex items-center justify-between mb-6 flex-wrap gap-4">
                <div class="toggle-container">
                    <div class="toggle-slider"></div>
                    <button id="douban-movie-toggle" class="toggle-button active" aria-pressed="true" role="button">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <rect x="2" y="3" width="20" height="14" rx="2" ry="2"></rect>
                            <line x1="8" y1="21" x2="16" y2="21"></line>
                            <line x1="12" y1="17" x2="12" y2="21"></line>
                        </svg>
                        电影
                    </button>
                    <button id="douban-tv-toggle" class="toggle-button" aria-pressed="false" role="button">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                            <rect x="2" y="3" width="20" height="14" rx="2" ry="2"></rect>
                            <line x1="8" y1="21" x2="16" y2="21"></line>
                            <line x1="12" y1="17" x2="12" y2="21"></line>
                        </svg>
                        电视剧
                    </button>
                </div>
            </div>

            <div class="flex items-start justify-between mb-6 flex-wrap gap-4">
                <div id="douban-tags" class="flex flex-wrap gap-2 flex-1"></div>
                <button id="douban-refresh" class="btn btn-secondary btn-sm" aria-label="换一批推荐内容">
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <path d="M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8"></path>
                        <path d="M21 3v5h-5"></path>
                        <path d="M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16"></path>
                        <path d="M3 21v-5h5"></path>
                    </svg>
                    换一批
                </button>
            </div>

            <div id="douban-results" class="card-grid" style="min-height: 300px;">
                <!-- Douban results will be loaded here -->
            </div>
        </div>
    </div>
</section>
{| end block content |}

{| block style |}
<style>
    /* Enhanced Douban Tab Styles */
    #douban-tags .btn {
        position: relative;
        overflow: hidden;
        background-color: var(--bg-card);
        color: var(--text-secondary);
        border: 1px solid var(--border);
        padding: var(--spacing-2) var(--spacing-4);
        border-radius: var(--radius-full);
        font-size: var(--font-size-sm);
        font-weight: var(--font-weight-medium);
        transition: all var(--duration-200) var(--transition-ease);
        cursor: pointer;
    }

    #douban-tags .btn::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(135deg, var(--primary) 0%, var(--primary-dark) 100%);
        transform: scaleX(0);
        transform-origin: left;
        transition: transform var(--duration-300) var(--transition-ease);
        z-index: -1;
    }

    #douban-tags .btn.active {
        background-color: var(--primary);
        color: white;
        border-color: var(--primary);
        transform: translateY(-1px);
        box-shadow: var(--shadow-md);
    }

    #douban-tags .btn.active::before {
        transform: scaleX(1);
    }

    #douban-tags .btn:not(.active):hover {
        background-color: var(--bg-card-hover);
        color: var(--text-primary);
        border-color: var(--border-light);
        transform: translateY(-1px);
    }

    /* Enhanced Loading States for Douban */
    #douban-results.loading {
        position: relative;
        min-height: 300px;
    }

    #douban-results.loading::after {
        content: '';
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        width: 40px;
        height: 40px;
        border: 3px solid var(--border);
        border-top: 3px solid var(--primary);
        border-radius: 50%;
        animation: spin 1s linear infinite;
    }

    @keyframes spin {
        0% { transform: translate(-50%, -50%) rotate(0deg); }
        100% { transform: translate(-50%, -50%) rotate(360deg); }
    }

    /* Enhanced Tab Button Icons */
    .tab-button svg {
        margin-right: var(--spacing-2);
        transition: transform var(--duration-200) var(--transition-ease);
    }

    .tab-button:hover svg {
        transform: scale(1.1);
    }

    .tab-button.active svg {
        color: var(--primary);
    }

    /* Enhanced Toggle Button Icons */
    .toggle-button svg {
        margin-right: var(--spacing-1-5);
        transition: transform var(--duration-200) var(--transition-ease);
    }

    .toggle-button:hover svg {
        transform: scale(1.05);
    }

    .toggle-button.active svg {
        color: white;
    }

    /* Enhanced Refresh Button */
    #douban-refresh svg {
        transition: transform var(--duration-300) var(--transition-ease);
    }

    #douban-refresh:hover svg {
        transform: rotate(180deg);
    }

    /* Enhanced Mobile Styles for Douban Filter Buttons */
    @media (max-width: 768px) {
        /* Mobile layout adjustments */
        .flex.items-start.justify-between.mb-6.flex-wrap.gap-4 {
            flex-direction: column;
            gap: var(--spacing-4);
        }

        #douban-tags {
            /* Horizontal scrolling container for mobile */
            position: relative;
            display: flex;
            flex-wrap: nowrap;
            gap: var(--spacing-1-5);
            overflow-x: auto;
            padding: var(--spacing-2) 0;
            margin: 0 calc(-1 * var(--spacing-4));
            padding-left: var(--spacing-4);
            padding-right: var(--spacing-4);
            scrollbar-width: none;
            -ms-overflow-style: none;
            -webkit-overflow-scrolling: touch;
        }

        #douban-tags::-webkit-scrollbar {
            display: none;
        }

        /* Add subtle fade effect to indicate scrollability */
        #douban-tags::after {
            content: '';
            position: absolute;
            top: 0;
            right: 0;
            width: 20px;
            height: 100%;
            background: linear-gradient(to left, var(--bg-card), transparent);
            pointer-events: none;
            z-index: 1;
        }

        /* Container for the fade effect */
        .flex.items-start.justify-between.mb-6.flex-wrap.gap-4 {
            position: relative;
        }

        #douban-tags .btn {
            /* Enhanced touch targets while preserving design */
            min-height: 44px;
            padding: var(--spacing-2-5) var(--spacing-3);
            font-size: var(--font-size-sm);
            min-width: auto;
            font-weight: var(--font-weight-medium);
            border-width: 1px;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            white-space: nowrap;
            flex-shrink: 0;
        }

        /* Enhanced touch feedback for mobile */
        #douban-tags .btn:hover,
        #douban-tags .btn:focus {
            transform: translateY(-2px);
            box-shadow: var(--shadow-lg);
        }

        #douban-tags .btn.active {
            transform: translateY(-1px);
            box-shadow: var(--shadow-md);
        }

        /* Make toggle buttons larger too */
        .toggle-button {
            min-height: 44px;
            padding: var(--spacing-3) var(--spacing-4);
            font-size: var(--font-size-base);
        }

        /* Make refresh button larger and full width */
        #douban-refresh {
            min-height: 44px;
            padding: var(--spacing-3) var(--spacing-4);
            font-size: var(--font-size-base);
            width: 100%;
            margin-top: 0;
        }
    }

    /* Responsive Enhancements */
    @media (max-width: 640px) {
        .tab-button {
            font-size: var(--font-size-sm);
            padding: var(--spacing-2) var(--spacing-3);
        }

        .tab-button svg {
            width: 14px;
            height: 14px;
        }

        .toggle-button {
            padding: var(--spacing-2) var(--spacing-3);
            font-size: var(--font-size-sm);
        }

        .toggle-button svg {
            width: 14px;
            height: 14px;
            margin-right: var(--spacing-1);
        }

        #douban-refresh {
            width: 100%;
            margin-top: var(--spacing-2);
        }

        /* Extra small mobile adjustments for Douban tags */
        #douban-tags {
            gap: var(--spacing-1);
            margin: 0 calc(-1 * var(--spacing-3));
            padding-left: var(--spacing-3);
            padding-right: var(--spacing-3);
        }

        #douban-tags .btn {
            padding: var(--spacing-2) var(--spacing-2-5);
            min-height: 42px;
            font-size: var(--font-size-sm);
            min-width: auto;
        }

        .toggle-button {
            min-height: 42px;
            padding: var(--spacing-2-5) var(--spacing-3);
            font-size: var(--font-size-sm);
        }

        #douban-refresh {
            min-height: 42px;
            padding: var(--spacing-2-5) var(--spacing-3);
            font-size: var(--font-size-sm);
        }
    }
</style>
{| end block style |}

{| block script |}
<script>
    // Tab switching functionality with smooth animations
    document.addEventListener('DOMContentLoaded', function() {
        const tabButtons = document.querySelectorAll('.tab-button');
        const tabContents = document.querySelectorAll('.tab-content');

        tabButtons.forEach(button => {
            button.addEventListener('click', function() {
                const tabName = this.getAttribute('data-tab');

                // Don't do anything if the current tab is already active
                if (this.classList.contains('active')) {
                    return;
                }

                // Update tabs UI
                tabButtons.forEach(btn => {
                    btn.classList.remove('active');
                    btn.setAttribute('aria-selected', 'false');
                });
                tabContents.forEach(content => {
                    content.classList.remove('active');
                });

                // Activate current tab
                this.classList.add('active');
                this.setAttribute('aria-selected', 'true');
                document.getElementById(tabName + '-tab').classList.add('active');

                // Initialize Douban tab if needed
                if (tabName === 'douban' && typeof initDoubanTab === 'function') {
                    setTimeout(initDoubanTab, 100);
                }
            });
        });

        // Handle refresh button icon animation
        const refreshButton = document.getElementById('douban-refresh');
        if (refreshButton) {
            refreshButton.addEventListener('mouseover', function() {
                const icon = this.querySelector('span');
                if (icon) icon.style.transform = 'rotate(90deg)';
            });

            refreshButton.addEventListener('mouseout', function() {
                const icon = this.querySelector('span');
                if (icon) icon.style.transform = 'rotate(0deg)';
            });
        }

        // Initialize toggle slider state
        const toggleSlider = document.querySelector('.toggle-slider');
        const tvToggle = document.getElementById('douban-tv-toggle');
        const movieToggle = document.getElementById('douban-movie-toggle');

        if (toggleSlider && tvToggle && movieToggle) {
            toggleSlider.style.transform = tvToggle.classList.contains('active') ? 'translateX(100%)' : 'translateX(0)';
            toggleSlider.classList.toggle('right', tvToggle.classList.contains('active'));
        }
    });
</script>
<script src="/statics/vod/douban_tab_new.js?v=2"></script>
<script src="/statics/optimized-history.js"></script>
{| end block script |}
