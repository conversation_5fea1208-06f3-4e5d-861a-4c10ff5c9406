/**
 * Enhanced Application JavaScript
 * Consolidated and optimized functionality for the VOD application
 */

// Enhanced Application Namespace
const EnhancedApp = {
  // Configuration
  config: {
    breakpoints: {
      mobile: 768,
      tablet: 1024,
      desktop: 1200
    },
    animations: {
      duration: {
        fast: 150,
        normal: 300,
        slow: 500
      },
      easing: 'cubic-bezier(0.4, 0, 0.2, 1)'
    }
  },

  // Utility functions
  utils: {
    // Device detection
    isMobile: () => window.innerWidth <= EnhancedApp.config.breakpoints.mobile,
    isTablet: () => window.innerWidth <= EnhancedApp.config.breakpoints.tablet,
    isDesktop: () => window.innerWidth > EnhancedApp.config.breakpoints.tablet,

    // DOM utilities
    $: (selector) => document.querySelector(selector),
    $$: (selector) => document.querySelectorAll(selector),
    
    // Create element with classes and attributes
    createElement: (tag, classes = '', attributes = {}) => {
      const element = document.createElement(tag);
      if (classes) element.className = classes;
      Object.entries(attributes).forEach(([key, value]) => {
        element.setAttribute(key, value);
      });
      return element;
    },

    // Debounce function
    debounce: (func, wait) => {
      let timeout;
      return function executedFunction(...args) {
        const later = () => {
          clearTimeout(timeout);
          func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
      };
    },

    // Throttle function
    throttle: (func, limit) => {
      let inThrottle;
      return function() {
        const args = arguments;
        const context = this;
        if (!inThrottle) {
          func.apply(context, args);
          inThrottle = true;
          setTimeout(() => inThrottle = false, limit);
        }
      };
    },

    // Animation utilities
    fadeIn: (element, duration = 300) => {
      element.style.opacity = '0';
      element.style.display = 'block';
      element.style.transition = `opacity ${duration}ms ease`;
      
      requestAnimationFrame(() => {
        element.style.opacity = '1';
      });
    },

    fadeOut: (element, duration = 300) => {
      element.style.transition = `opacity ${duration}ms ease`;
      element.style.opacity = '0';
      
      setTimeout(() => {
        element.style.display = 'none';
      }, duration);
    },

    // Slide animations
    slideDown: (element, duration = 300) => {
      element.style.height = '0';
      element.style.overflow = 'hidden';
      element.style.transition = `height ${duration}ms ease`;
      element.style.display = 'block';
      
      const height = element.scrollHeight;
      requestAnimationFrame(() => {
        element.style.height = height + 'px';
      });
      
      setTimeout(() => {
        element.style.height = 'auto';
        element.style.overflow = '';
      }, duration);
    },

    slideUp: (element, duration = 300) => {
      element.style.height = element.scrollHeight + 'px';
      element.style.overflow = 'hidden';
      element.style.transition = `height ${duration}ms ease`;
      
      requestAnimationFrame(() => {
        element.style.height = '0';
      });
      
      setTimeout(() => {
        element.style.display = 'none';
        element.style.height = '';
        element.style.overflow = '';
      }, duration);
    }
  },

  // Enhanced UI components
  ui: {
    // Loading spinner
    createLoadingSpinner: (size = 'normal') => {
      const sizes = {
        small: '20px',
        normal: '32px',
        large: '48px'
      };
      
      const spinner = EnhancedApp.utils.createElement('div', 'loading-spinner');
      spinner.style.width = sizes[size];
      spinner.style.height = sizes[size];
      return spinner;
    },

    // Toast notifications
    showToast: (message, type = 'info', duration = 3000) => {
      const toast = EnhancedApp.utils.createElement('div', `toast toast-${type}`);
      toast.textContent = message;
      
      // Add to DOM
      document.body.appendChild(toast);
      
      // Animate in
      EnhancedApp.utils.fadeIn(toast);
      
      // Auto remove
      setTimeout(() => {
        EnhancedApp.utils.fadeOut(toast);
        setTimeout(() => {
          if (toast.parentNode) {
            toast.parentNode.removeChild(toast);
          }
        }, 300);
      }, duration);
    },

    // Modal utilities
    openModal: (modalId) => {
      const modal = EnhancedApp.utils.$(modalId);
      if (modal) {
        modal.classList.add('active');
        document.body.style.overflow = 'hidden';
        
        // Focus management
        const firstFocusable = modal.querySelector('button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])');
        if (firstFocusable) {
          firstFocusable.focus();
        }
      }
    },

    closeModal: (modalId) => {
      const modal = EnhancedApp.utils.$(modalId);
      if (modal) {
        modal.classList.remove('active');
        document.body.style.overflow = '';
      }
    },

    // Enhanced dropdown
    initDropdown: (triggerSelector, contentSelector) => {
      const trigger = EnhancedApp.utils.$(triggerSelector);
      const content = EnhancedApp.utils.$(contentSelector);
      
      if (!trigger || !content) return;
      
      trigger.addEventListener('click', (e) => {
        e.preventDefault();
        e.stopPropagation();
        
        const isOpen = content.classList.contains('show');
        
        // Close all other dropdowns
        EnhancedApp.utils.$$('.dropdown-content.show').forEach(dropdown => {
          dropdown.classList.remove('show');
        });
        
        if (!isOpen) {
          content.classList.add('show');
          
          // Position dropdown
          const rect = trigger.getBoundingClientRect();
          const contentRect = content.getBoundingClientRect();
          
          // Check if dropdown fits below trigger
          if (rect.bottom + contentRect.height > window.innerHeight) {
            content.style.top = (rect.top - contentRect.height) + 'px';
          } else {
            content.style.top = rect.bottom + 'px';
          }
          
          content.style.left = rect.left + 'px';
        }
      });
      
      // Close on outside click
      document.addEventListener('click', (e) => {
        if (!trigger.contains(e.target) && !content.contains(e.target)) {
          content.classList.remove('show');
        }
      });
      
      // Close on escape key
      document.addEventListener('keydown', (e) => {
        if (e.key === 'Escape' && content.classList.contains('show')) {
          content.classList.remove('show');
          trigger.focus();
        }
      });
    }
  },

  // Performance optimizations
  performance: {
    // Lazy load images
    initLazyLoading: () => {
      if ('IntersectionObserver' in window) {
        const imageObserver = new IntersectionObserver((entries, observer) => {
          entries.forEach(entry => {
            if (entry.isIntersecting) {
              const img = entry.target;
              img.src = img.dataset.src;
              img.classList.remove('lazy');
              observer.unobserve(img);
            }
          });
        });

        EnhancedApp.utils.$$('img[data-src]').forEach(img => {
          imageObserver.observe(img);
        });
      }
    },

    // Optimize animations based on device capabilities
    optimizeAnimations: () => {
      // Reduce animations on low-end devices
      if (navigator.hardwareConcurrency && navigator.hardwareConcurrency < 4) {
        document.body.classList.add('reduced-animations');
      }
      
      // Respect user's motion preferences
      if (window.matchMedia('(prefers-reduced-motion: reduce)').matches) {
        document.body.classList.add('reduced-motion');
      }
    },

    // Preload critical resources
    preloadResources: (resources) => {
      resources.forEach(resource => {
        const link = document.createElement('link');
        link.rel = 'preload';
        link.href = resource.href;
        link.as = resource.as;
        if (resource.type) link.type = resource.type;
        document.head.appendChild(link);
      });
    }
  },

  // Enhanced accessibility
  accessibility: {
    // Keyboard navigation
    initKeyboardNavigation: () => {
      // Tab navigation for custom components
      document.addEventListener('keydown', (e) => {
        if (e.key === 'Tab') {
          document.body.classList.add('keyboard-navigation');
        }
      });
      
      document.addEventListener('mousedown', () => {
        document.body.classList.remove('keyboard-navigation');
      });
    },

    // Screen reader announcements
    announce: (message, priority = 'polite') => {
      const announcer = EnhancedApp.utils.createElement('div', 'sr-only');
      announcer.setAttribute('aria-live', priority);
      announcer.textContent = message;
      
      document.body.appendChild(announcer);
      
      setTimeout(() => {
        document.body.removeChild(announcer);
      }, 1000);
    },

    // Focus management
    trapFocus: (container) => {
      const focusableElements = container.querySelectorAll(
        'button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])'
      );
      
      const firstFocusable = focusableElements[0];
      const lastFocusable = focusableElements[focusableElements.length - 1];
      
      container.addEventListener('keydown', (e) => {
        if (e.key === 'Tab') {
          if (e.shiftKey) {
            if (document.activeElement === firstFocusable) {
              lastFocusable.focus();
              e.preventDefault();
            }
          } else {
            if (document.activeElement === lastFocusable) {
              firstFocusable.focus();
              e.preventDefault();
            }
          }
        }
      });
    }
  }
};

// Initialize enhanced app
document.addEventListener('DOMContentLoaded', () => {
  // Initialize performance optimizations
  EnhancedApp.performance.optimizeAnimations();
  EnhancedApp.performance.initLazyLoading();
  
  // Initialize accessibility features
  EnhancedApp.accessibility.initKeyboardNavigation();
  
  // Initialize responsive behavior
  const handleResize = EnhancedApp.utils.throttle(() => {
    // Update mobile/desktop specific behaviors
    const isMobile = EnhancedApp.utils.isMobile();
    document.body.classList.toggle('is-mobile', isMobile);
    document.body.classList.toggle('is-desktop', !isMobile);
  }, 250);
  
  window.addEventListener('resize', handleResize);
  handleResize(); // Initial call
});

// Make EnhancedApp globally available
window.EnhancedApp = EnhancedApp;
