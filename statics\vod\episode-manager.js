/**
 * Episode Manager - Handles episode pagination and watched status
 */
document.addEventListener('DOMContentLoaded', function() {
    // Initialize watched status first
    updateWatchedEpisodes();

    // Then initialize pagination (so watched status is already applied)
    initEpisodePagination();

    // Initialize tab switching with scroll functionality
    initTabSwitchingWithScroll();

    // Scroll to active episode after everything is loaded
    setTimeout(scrollToActiveEpisode, 300);

    // Also scroll when images are loaded (in case layout changes)
    window.addEventListener('load', function() {
        setTimeout(scrollToActiveEpisode, 100);
    });
});

/**
 * Initialize tab switching with scroll functionality
 */
function initTabSwitchingWithScroll() {
    // Find all tab buttons
    const tabButtons = document.querySelectorAll('.tab-button, .tabs-head-btn');

    tabButtons.forEach(button => {
        // Add click event listener
        button.addEventListener('click', function() {
            // Wait for tab content to be shown, then scroll to active episode
            setTimeout(() => {
                scrollToActiveEpisode();
            }, 150);
        });
    });

    // Also listen for tab changes via other methods (like hover)
    const observer = new MutationObserver(function(mutations) {
        mutations.forEach(function(mutation) {
            if (mutation.type === 'attributes' &&
                (mutation.attributeName === 'class' || mutation.attributeName === 'style')) {
                const target = mutation.target;

                // Check if a tab content became active
                if (target.classList.contains('tab-content') &&
                    (target.classList.contains('active') || target.classList.contains('tabs-curr'))) {
                    setTimeout(scrollToActiveEpisode, 100);
                }
            }
        });
    });

    // Observe tab content changes
    const tabContents = document.querySelectorAll('.tab-content, .vod-episodes');
    tabContents.forEach(content => {
        observer.observe(content, {
            attributes: true,
            attributeFilter: ['class', 'style']
        });
    });
}

/**
 * Initialize episode pagination functionality
 */
function initEpisodePagination() {
    // Get all pagination buttons
    const paginationButtons = document.querySelectorAll('.episode-page-button');

    // Add click event to each button
    paginationButtons.forEach(button => {
        button.addEventListener('click', function() {
            const page = this.getAttribute('data-page');
            const channel = this.getAttribute('data-channel');

            // Remove active class from all buttons in this channel
            document.querySelectorAll(`.episodes-pagination[data-channel="${channel}"] .episode-page-button`).forEach(btn => {
                btn.classList.remove('active');
            });

            // Add active class to clicked button
            this.classList.add('active');

            // Show episodes for this page
            showEpisodesForPage(channel, page);
        });
    });

    // Check if there's a page parameter in the URL
    const urlParams = new URLSearchParams(window.location.search);
    const urlPage = urlParams.get('page');
    const urlChannel = urlParams.get('ch');

    // For each channel, show the active page
    const channels = new Set();
    paginationButtons.forEach(button => {
        channels.add(button.getAttribute('data-channel'));
    });

    // For each channel, show the active page
    channels.forEach(channel => {
        // If this is the channel from the URL and there's a page parameter, use that
        if (channel === urlChannel && urlPage) {
            const pageButton = document.querySelector(`.episodes-pagination[data-channel="${channel}"] .episode-page-button[data-page="${urlPage}"]`);
            if (pageButton) {
                // Remove active class from all buttons in this channel
                document.querySelectorAll(`.episodes-pagination[data-channel="${channel}"] .episode-page-button`).forEach(btn => {
                    btn.classList.remove('active');
                });

                // Add active class to this button
                pageButton.classList.add('active');

                // Show episodes for this page
                showEpisodesForPage(channel, urlPage);
                return;
            }
        }

        // Otherwise, use the active button or the first button
        const activeButton = document.querySelector(`.episodes-pagination[data-channel="${channel}"] .episode-page-button.active`);
        if (activeButton) {
            const page = activeButton.getAttribute('data-page');
            showEpisodesForPage(channel, page);
        } else {
            // If no active button, show the first page
            const firstButton = document.querySelector(`.episodes-pagination[data-channel="${channel}"] .episode-page-button`);
            if (firstButton) {
                firstButton.classList.add('active');
                showEpisodesForPage(channel, firstButton.getAttribute('data-page'));
            }
        }
    });
}

/**
 * Show episodes for the selected page
 * @param {string} channel - The channel code
 * @param {number} page - The page number to show
 */
function showEpisodesForPage(channel, page) {
    // Get all episode buttons for this channel
    const allEpisodes = document.querySelectorAll(`.tab-content[data-tab-id="${channel}"] .episode-button`);

    // Get episodes per page
    const episodesPerPage = 24;

    // Convert page to number
    const pageNum = parseInt(page, 10);

    // Calculate start and end indices
    const startIdx = (pageNum - 1) * episodesPerPage;
    const endIdx = pageNum * episodesPerPage - 1;

    // Hide all episodes
    allEpisodes.forEach((episode, index) => {
        if (index >= startIdx && index <= endIdx) {
            episode.style.display = '';
        } else {
            episode.style.display = 'none';
        }
    });

    // Update URL with the current page (without reloading)
    try {
        const urlParams = new URLSearchParams(window.location.search);
        if (urlParams.get('ch') === channel) {
            // Only update if we're on this channel
            const url = new URL(window.location.href);
            url.searchParams.set('page', pageNum);
            window.history.replaceState({}, '', url);
        }
    } catch (error) {
        // Error updating URL
    }

    // Scroll to the top of the episodes grid for better UX
    const episodesGrid = document.querySelector(`.tab-content[data-tab-id="${channel}"] .episodes-grid`);
    if (episodesGrid) {
        // Smooth scroll to the top of the episodes grid
        episodesGrid.scrollIntoView({ behavior: 'smooth', block: 'start' });

        // Also scroll to the active episode if there is one
        setTimeout(scrollToActiveEpisode, 100);
    }
}

/**
 * Update watched status for episodes
 */
function updateWatchedEpisodes() {
    try {
        // Get the VOD ID from the URL
        const urlParams = new URLSearchParams(window.location.search);
        const vodId = urlParams.get('id');

        if (!vodId) return;

        // Get play cache from localStorage
        const playCache = JSON.parse(localStorage.getItem('vodg-play') || '{}');

        // If no cache for this VOD, return
        if (!playCache[vodId]) return;

        // Loop through all episodes
        document.querySelectorAll('.episode-button').forEach(episode => {
            const episodeNum = episode.getAttribute('data-episode');

            // If this episode has been watched
            if (playCache[vodId][episodeNum] && playCache[vodId][episodeNum].playProgressRate > 0) {
                // Add watched class
                episode.classList.add('watched');

                // If progress is more than 90%, consider it fully watched
                if (playCache[vodId][episodeNum].playProgressRate > 0.9) {
                    episode.classList.add('fully-watched');
                }
            }
        });
    } catch (error) {
        // Error updating watched episodes
    }
}

/**
 * Enhanced scroll to active episode function
 * Works with multiple selector patterns and improved positioning
 */
function scrollToActiveEpisode() {
    try {
        console.log('🔍 Starting scroll to active episode...');

        // Simple and direct approach - find the active episode
        let activeEpisode = null;

        // First, try to find the current episode in the visible tab
        const visibleEpisodesGrid = document.querySelector('.tab-content.active .episodes-grid, .vod-episodes.active .episodes-grid, .tab-content.tabs-curr .episodes-grid, .vod-episodes.tabs-curr .episodes-grid');

        if (visibleEpisodesGrid) {
            console.log('📦 Found visible episodes grid');

            // Look for active episode within this grid
            activeEpisode = visibleEpisodesGrid.querySelector('.vod-episode-curr, .episode-button.vod-episode-curr, .vod-episode.active, .episode-button.active');

            if (activeEpisode) {
                console.log('🎯 Found active episode in visible grid:', activeEpisode.textContent?.trim());

                // Simple scroll calculation - just scroll the episode into view
                const episodeRect = activeEpisode.getBoundingClientRect();
                const gridRect = visibleEpisodesGrid.getBoundingClientRect();

                // Check if episode is already visible
                const isVisible = (
                    episodeRect.top >= gridRect.top &&
                    episodeRect.bottom <= gridRect.bottom
                );

                if (!isVisible) {
                    console.log('📍 Episode not visible, scrolling...');

                    // Use scrollIntoView for simplicity and reliability
                    activeEpisode.scrollIntoView({
                        behavior: 'smooth',
                        block: 'center',
                        inline: 'nearest'
                    });

                    console.log('✅ Scroll command executed');
                } else {
                    console.log('✅ Episode already visible');
                }

                return;
            }
        }

        // Fallback: global search
        console.log('🔍 Fallback: searching globally...');
        activeEpisode = document.querySelector('.vod-episode-curr, .episode-button.vod-episode-curr, .vod-episode.active, .episode-button.active');

        if (activeEpisode) {
            console.log('🎯 Found active episode globally:', activeEpisode.textContent?.trim());

            // Simple scroll into view
            activeEpisode.scrollIntoView({
                behavior: 'smooth',
                block: 'center',
                inline: 'nearest'
            });

            console.log('✅ Global scroll command executed');
        } else {
            console.log('❌ No active episode found');

            // Debug: show what episodes exist
            const allEpisodes = document.querySelectorAll('.episode-button, .vod-episode');
            console.log('📋 Total episodes found:', allEpisodes.length);

            if (allEpisodes.length > 0) {
                console.log('📋 First few episodes:');
                for (let i = 0; i < Math.min(5, allEpisodes.length); i++) {
                    const ep = allEpisodes[i];
                    console.log(`  ${i + 1}. "${ep.textContent?.trim()}" - classes: ${ep.className}`);
                }
            }
        }

    } catch (error) {
        console.error('❌ Error scrolling to active episode:', error);
    }
}

/**
 * Global function to scroll to active episode
 * Can be called from other scripts or console for debugging
 */
window.scrollToCurrentEpisode = function() {
    scrollToActiveEpisode();
};

/**
 * Global function to scroll to a specific episode
 * @param {string} episodeSelector - CSS selector for the episode
 */
window.scrollToEpisode = function(episodeSelector) {
    try {
        const episode = document.querySelector(episodeSelector);
        if (!episode) {
            console.log('Episode not found:', episodeSelector);
            return;
        }

        const episodesGrid = episode.closest('.episodes-grid');
        if (!episodesGrid) {
            console.log('Episodes grid not found');
            return;
        }

        // Calculate scroll position
        const episodeOffsetTop = episode.offsetTop;
        const gridHeight = episodesGrid.clientHeight;
        const episodeHeight = episode.offsetHeight;
        const targetScroll = episodeOffsetTop - (gridHeight / 2) + (episodeHeight / 2);
        const maxScroll = episodesGrid.scrollHeight - gridHeight;
        const finalScroll = Math.max(0, Math.min(targetScroll, maxScroll));

        episodesGrid.scrollTo({
            top: finalScroll,
            behavior: 'smooth'
        });
    } catch (error) {
        console.error('Error scrolling to episode:', error);
    }
};

/**
 * Update watched status for a specific episode
 * This function can be called from play.html to update the status in real-time
 * @param {string} vodId - The VOD ID
 * @param {string} episodeNum - The episode number
 * @param {number} progress - The watch progress (0-1)
 */
window.updateEpisodeWatchStatus = function(vodId, episodeNum, progress) {
    try {
        // If we're on the detail page for this VOD
        const urlParams = new URLSearchParams(window.location.search);
        const currentVodId = urlParams.get('id');

        if (currentVodId !== vodId) return;

        // Find the episode button
        const episode = document.querySelector(`.episode-button[data-episode="${episodeNum}"]`);

        if (!episode) return;

        // Update classes based on progress
        if (progress > 0) {
            episode.classList.add('watched');

            if (progress > 0.9) {
                episode.classList.add('fully-watched');
            } else {
                episode.classList.remove('fully-watched');
            }
        } else {
            episode.classList.remove('watched', 'fully-watched');
        }
    } catch (error) {
        // Error updating episode watch status
    }
};
