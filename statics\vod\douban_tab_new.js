// 豆瓣推荐标签页实现 - 简化版

// 豆瓣标签列表
const movieTags = ['热门', '最新', '经典', '豆瓣高分', '冷门佳片', '华语', '欧美', '韩国', '日本', '动作', '喜剧', '爱情', '科幻', '悬疑', '恐怖', '治愈'];
const tvTags = ['热门', '美剧', '英剧', '韩剧', '日剧', '国产剧', '港剧', '日本动画', '综艺', '纪录片'];

// 代理地址，用于绕过CORS
const PROXY_URL = 'https://cors.zme.ink/';

// 常量定义
const PAGE_SIZE = 16; // 一次显示的项目数量
const FETCH_TIMEOUT = 10000; // 10秒超时

// 当前状态
let currentType = 'movie'; // 'movie' or 'tv'
let currentTag = '热门';
let pageStart = 0;
let isInitialized = false; // 初始化标记，防止重复初始化

// 添加通用动画样式
const addStyles = () => {
    const styleId = 'douban-tab-styles';
    if (document.getElementById(styleId)) return;

    const style = document.createElement('style');
    style.id = styleId;
    style.textContent = `
        @keyframes douban-spinner {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        @keyframes douban-loading-pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }
        @keyframes skeleton-shine {
            0% { transform: translateX(-100%); }
            100% { transform: translateX(100%); }
        }
        @keyframes skeleton-fade-in {
            0% { opacity: 0; transform: translateY(10px); }
            100% { opacity: 1; transform: translateY(0); }
        }
        @keyframes fade-in {
            0% { opacity: 0; transform: translateY(20px); }
            100% { opacity: 1; transform: translateY(0); }
        }

        .skeleton-poster {
            width: 100%;
            height: 0;
            padding-bottom: 150%;
            background-color: #2a2f3a;
            border-radius: 8px;
            position: relative;
            overflow: hidden;
        }
        .skeleton-poster::after, .skeleton-text::after {
            content: "";
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-image: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
            animation: skeleton-shine 1.5s infinite;
        }
        .skeleton-text {
            height: 16px;
            width: 90%;
            margin: 12px auto;
            background-color: #2a2f3a;
            border-radius: 4px;
            position: relative;
            overflow: hidden;
        }
        .douban-skeleton-card {
            animation: skeleton-fade-in 0.5s ease forwards;
            opacity: 0;
        }

        .douban-action-btn {
            padding: 0.75rem 1.5rem;
            background-color: var(--primary, #3b82f6);
            color: white;
            border: none;
            border-radius: var(--radius, 6px);
            cursor: pointer;
            font-weight: 500;
            font-size: 1rem;
            transition: all 0.2s ease;
            box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);
        }
        .douban-action-btn:hover {
            background-color: var(--primary-dark, #1d4ed8) !important;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4);
        }
        .douban-action-btn:active {
            transform: translateY(0);
            box-shadow: 0 2px 6px rgba(59, 130, 246, 0.2);
        }
    `;
    document.head.appendChild(style);
};

// 初始化豆瓣标签页
function initDoubanTab() {
    // 如果已经初始化过，直接返回
    if (isInitialized) return;

    // 获取必要的DOM元素
    const elements = {
        doubanTab: document.getElementById('douban-tab'),
        movieToggle: document.getElementById('douban-movie-toggle'),
        tvToggle: document.getElementById('douban-tv-toggle'),
        tagsContainer: document.getElementById('douban-tags'),
        refreshButton: document.getElementById('douban-refresh'),
        resultsContainer: document.getElementById('douban-results'),
        toggleSlider: document.querySelector('.toggle-slider')
    };

    // 检查必要的DOM元素是否存在
    const { doubanTab, movieToggle, tvToggle, tagsContainer, refreshButton, resultsContainer, toggleSlider } = elements;
    if (!doubanTab || !movieToggle || !tvToggle || !tagsContainer || !refreshButton || !resultsContainer) {
        return;
    }

    // 添加样式
    addStyles();

    // 设置初始状态
    movieToggle.classList.add('active');
    tvToggle.classList.remove('active');
    movieToggle.setAttribute('aria-pressed', 'true');
    tvToggle.setAttribute('aria-pressed', 'false');

    // 设置电影/电视剧切换按钮
    const setupTypeToggle = (button, type, isActive) => {
        button.addEventListener('click', function() {
            if (currentType !== type) {
                currentType = type;
            currentTag = '热门';
            pageStart = 0;

            // 更新UI
                if (isActive) {
            movieToggle.classList.add('active');
            tvToggle.classList.remove('active');
                    movieToggle.setAttribute('aria-pressed', 'true');
                    tvToggle.setAttribute('aria-pressed', 'false');
            toggleSlider.style.transform = 'translateX(0)';
            toggleSlider.classList.remove('right');
                } else {
            tvToggle.classList.add('active');
            movieToggle.classList.remove('active');
                    tvToggle.setAttribute('aria-pressed', 'true');
                    movieToggle.setAttribute('aria-pressed', 'false');
            toggleSlider.style.transform = 'translateX(100%)';
            toggleSlider.classList.add('right');
                }

            // 重新渲染
            renderTags();
            loadContent();
        }
    });
    };

    setupTypeToggle(movieToggle, 'movie', true);
    setupTypeToggle(tvToggle, 'tv', false);

    // 设置换一批按钮
    refreshButton.addEventListener('click', function() {
        pageStart += PAGE_SIZE;
        if (pageStart > 9 * PAGE_SIZE) {
            pageStart = 0;
        }
        loadContent();
    });

    // 渲染标签
    function renderTags() {
        // 清空容器
        tagsContainer.innerHTML = '';

        // 获取当前类型的标签
        const tags = currentType === 'movie' ? movieTags : tvTags;

        // 添加标签 - 使用增强的UI类
        tags.forEach(tag => {
            const button = document.createElement('button');
            button.className = `btn ${tag === currentTag ? 'active' : ''}`;

            if (tag === currentTag) {
                button.classList.add('active');
            }

            button.textContent = tag;
            button.setAttribute('role', 'tab');
            button.setAttribute('aria-selected', tag === currentTag ? 'true' : 'false');
            button.setAttribute('tabindex', tag === currentTag ? '0' : '-1');

            // 点击事件
            button.addEventListener('click', function() {
                if (currentTag !== tag) {
                    // 更新按钮状态
                    document.querySelectorAll('#douban-tags button').forEach(btn => {
                        btn.classList.remove('active');
                        btn.setAttribute('aria-selected', 'false');
                        btn.setAttribute('tabindex', '-1');
                    });

                    this.classList.add('active');
                    this.setAttribute('aria-selected', 'true');
                    this.setAttribute('tabindex', '0');
                    this.focus();

                    // 更新状态并加载
                    currentTag = tag;
                    pageStart = 0;
                    loadContent();
                }
            });

            // 键盘导航
            button.addEventListener('keydown', function(e) {
                if (e.key === 'Enter' || e.key === ' ') {
                    e.preventDefault();
                    this.click();
                }
            });

            tagsContainer.appendChild(button);
        });
    }

    // 确保结果容器样式正确
    function ensureResultsContainerStyle() {
        resultsContainer.style.display = 'grid';
        resultsContainer.style.gridTemplateColumns = 'repeat(auto-fill, minmax(140px, 1fr))';
        resultsContainer.style.gap = '1rem';
        resultsContainer.classList.add('fixed-grid');
        resultsContainer.innerHTML = '';
    }

    // 显示加载状态
    function showLoading() {
        ensureResultsContainerStyle();

        // 创建加载指示器覆盖层
        const loadingOverlay = document.createElement('div');
        loadingOverlay.className = 'douban-loading-overlay';
        loadingOverlay.style.position = 'absolute';
        loadingOverlay.style.inset = '0';
        loadingOverlay.style.backgroundColor = 'rgba(0,0,0,0.4)';
        loadingOverlay.style.backdropFilter = 'blur(4px)';
        loadingOverlay.style.WebkitBackdropFilter = 'blur(4px)';
        loadingOverlay.style.display = 'flex';
        loadingOverlay.style.alignItems = 'center';
        loadingOverlay.style.justifyContent = 'center';
        loadingOverlay.style.zIndex = '20';
        loadingOverlay.style.borderRadius = 'var(--radius-lg, 8px)';
        loadingOverlay.style.gridColumn = '1 / -1';
        loadingOverlay.style.transition = 'opacity 0.3s ease';

        // 创建加载动画容器
        const loadingContainer = document.createElement('div');
        loadingContainer.className = 'douban-loading-container';
        loadingContainer.style.display = 'flex';
        loadingContainer.style.flexDirection = 'column';
        loadingContainer.style.alignItems = 'center';
        loadingContainer.style.justifyContent = 'center';
        loadingContainer.style.padding = '24px';
        loadingContainer.style.backgroundColor = 'rgba(31, 41, 55, 0.85)';
        loadingContainer.style.borderRadius = '12px';
        loadingContainer.style.boxShadow = '0 8px 32px rgba(0, 0, 0, 0.2)';
        loadingContainer.style.animation = 'douban-loading-pulse 2s infinite ease-in-out';

        // 创建加载动画
        const loadingSpinner = document.createElement('div');
        loadingSpinner.className = 'douban-loading-spinner';
        loadingSpinner.style.width = '48px';
        loadingSpinner.style.height = '48px';
        loadingSpinner.style.borderRadius = '50%';
        loadingSpinner.style.border = '3px solid rgba(59, 130, 246, 0.2)';
        loadingSpinner.style.borderTop = '3px solid var(--primary, #3b82f6)';
        loadingSpinner.style.borderRight = '3px solid var(--primary, #3b82f6)';
        loadingSpinner.style.animation = 'douban-spinner 1s linear infinite';
        loadingSpinner.style.marginBottom = '16px';

        // 创建加载文本
        const loadingText = document.createElement('div');
        loadingText.className = 'douban-loading-text';
        loadingText.textContent = '加载豆瓣推荐中...';
        loadingText.style.color = 'var(--text-primary, #f9fafb)';
        loadingText.style.fontWeight = '500';
        loadingText.style.fontSize = '16px';

        // 组装加载指示器
        loadingContainer.appendChild(loadingSpinner);
        loadingContainer.appendChild(loadingText);
        loadingOverlay.appendChild(loadingContainer);
        resultsContainer.appendChild(loadingOverlay);

        // 添加骨架屏卡片
        for (let i = 0; i < 12; i++) {
            const skeletonCard = document.createElement('div');
            skeletonCard.className = 'media-card douban-skeleton-card';
            skeletonCard.style.width = '100%';
            skeletonCard.style.margin = '0';
            skeletonCard.style.backgroundColor = 'var(--bg-card, #1f2937)';
            skeletonCard.style.borderRadius = '8px';
            skeletonCard.style.overflow = 'hidden';
            skeletonCard.style.boxShadow = '0 2px 8px rgba(0, 0, 0, 0.1)';
            skeletonCard.style.animationDelay = `${i * 0.05}s`;
            skeletonCard.innerHTML = `
                <div class="skeleton-poster"></div>
                <div class="skeleton-text"></div>
            `;
            resultsContainer.appendChild(skeletonCard);
        }
    }

    // 创建状态消息容器
    function createMessageContainer(iconSvg, message, tip) {
        const container = document.createElement('div');
        container.className = 'douban-message-container';
        container.style.textAlign = 'center';
        container.style.padding = '3rem 1rem';
        container.style.backgroundColor = 'var(--bg-card, #1f2937)';
        container.style.borderRadius = 'var(--radius-lg, 8px)';
        container.style.gridColumn = '1 / -1';
        container.style.boxShadow = '0 4px 16px rgba(0, 0, 0, 0.1)';
        container.style.animation = 'fade-in 0.5s ease forwards';

        // 创建图标
        const iconElement = document.createElement('div');
        iconElement.className = 'douban-message-icon';
        iconElement.innerHTML = iconSvg;

        // 创建消息
        const messageElement = document.createElement('div');
        messageElement.className = 'douban-message-text';
        messageElement.style.fontSize = '1.25rem';
        messageElement.style.fontWeight = '500';
        messageElement.style.margin = '1rem 0';
        messageElement.textContent = message;

        // 创建提示
        const tipElement = document.createElement('div');
        tipElement.className = 'douban-message-tip';
        tipElement.style.color = 'var(--text-tertiary, #9ca3af)';
        tipElement.style.fontSize = '0.875rem';
        tipElement.style.margin = '0.5rem 0 1.5rem';
        tipElement.textContent = tip;

        // 组装容器
        container.appendChild(iconElement);
        container.appendChild(messageElement);
        container.appendChild(tipElement);

        return container;
    }

    // 显示错误状态
    function showError(message) {
        ensureResultsContainerStyle();

        const errorContainer = createMessageContainer(
            `<svg xmlns="http://www.w3.org/2000/svg" width="64" height="64" viewBox="0 0 24 24" fill="none" stroke="#f87171" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <circle cx="12" cy="12" r="10"></circle>
                <line x1="12" y1="8" x2="12" y2="12"></line>
                <line x1="12" y1="16" x2="12.01" y2="16"></line>
            </svg>`,
            message,
            '提示：使用VPN可能有助于解决此问题'
        );

        errorContainer.querySelector('.douban-message-text').style.color = '#f87171';

        // 创建重试按钮
        const retryButton = document.createElement('button');
        retryButton.id = 'retry-error-btn';
        retryButton.className = 'douban-action-btn';
        retryButton.innerHTML = `
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" style="margin-right: 8px; vertical-align: -3px;">
                <path d="M21 2v6h-6"></path>
                <path d="M3 12a9 9 0 0 1 15-6.7l3-3"></path>
                <path d="M3 22v-6h6"></path>
                <path d="M21 12a9 9 0 0 1-15 6.7l-3 3"></path>
            </svg>
            重试
        `;

        errorContainer.appendChild(retryButton);
        resultsContainer.appendChild(errorContainer);

        // 添加重试按钮事件
        setTimeout(() => {
            const retryBtn = document.getElementById('retry-error-btn');
            if (retryBtn) {
                retryBtn.addEventListener('click', loadContent);
            }
        }, 0);
    }

    // 显示空结果状态
    function showEmpty() {
        ensureResultsContainerStyle();

        const emptyContainer = createMessageContainer(
            `<svg xmlns="http://www.w3.org/2000/svg" width="64" height="64" viewBox="0 0 24 24" fill="none" stroke="#9ca3af" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <rect x="2" y="2" width="20" height="20" rx="2.18" ry="2.18"></rect>
                <line x1="7" y1="2" x2="7" y2="22"></line>
                <line x1="17" y1="2" x2="17" y2="22"></line>
                <line x1="2" y1="12" x2="22" y2="12"></line>
                <line x1="2" y1="7" x2="7" y2="7"></line>
                <line x1="2" y1="17" x2="7" y2="17"></line>
                <line x1="17" y1="17" x2="22" y2="17"></line>
                <line x1="17" y1="7" x2="22" y2="7"></line>
            </svg>`,
            '暂无数据，请尝试其他分类或刷新',
            '可以尝试切换到其他标签或电影/电视剧类型'
        );

        emptyContainer.querySelector('.douban-message-text').style.color = 'var(--text-secondary, #e5e7eb)';

        // 创建换一批按钮
        const refreshButton = document.createElement('button');
        refreshButton.id = 'retry-empty-btn';
        refreshButton.className = 'douban-action-btn';
        refreshButton.innerHTML = `
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" style="margin-right: 8px; vertical-align: -3px;">
                <polyline points="1 4 1 10 7 10"></polyline>
                <polyline points="23 20 23 14 17 14"></polyline>
                <path d="M20.49 9A9 9 0 0 0 5.64 5.64L1 10m22 4l-4.64 4.36A9 9 0 0 1 3.51 15"></path>
            </svg>
            换一批
        `;

        emptyContainer.appendChild(refreshButton);
        resultsContainer.appendChild(emptyContainer);

        // 添加按钮事件
        setTimeout(() => {
            const retryBtn = document.getElementById('retry-empty-btn');
            if (retryBtn) {
                retryBtn.addEventListener('click', loadContent);
            }
        }, 0);
    }

    // 加载内容
    function loadContent() {
        showLoading();

        // 构建URL
        const url = `https://movie.douban.com/j/search_subjects?type=${currentType}&tag=${currentTag}&sort=recommend&page_limit=${PAGE_SIZE}&page_start=${pageStart}`;
        const fullUrl = PROXY_URL + encodeURIComponent(url);

        // 设置超时
        const timeoutPromise = new Promise((_, reject) => {
            setTimeout(() => reject(new Error('Request timeout')), FETCH_TIMEOUT);
        });

        // 获取数据
        Promise.race([
            fetch(fullUrl, {
                headers: {
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/121.0.0.0 Safari/537.36',
                    'Referer': 'https://movie.douban.com/',
                    'Accept': 'application/json, text/plain, */*',
                }
            }),
            timeoutPromise
        ])
        .then(response => {
            if (!response.ok) {
                throw new Error(`HTTP error! Status: ${response.status}`);
            }
            return response.json();
        })
        .then(renderCards)
        .catch(() => showError("获取豆瓣数据失败，请稍后重试"));
    }

    // 渲染卡片
    function renderCards(data) {
        ensureResultsContainerStyle();

        // 检查数据
        if (!data.subjects || data.subjects.length === 0) {
            showEmpty();
            return;
        }

        // 渲染卡片
        data.subjects.forEach(item => {
            // 安全处理数据
            const title = item.title ? item.title.replace(/</g, '&lt;').replace(/>/g, '&gt;').replace(/"/g, '&quot;') : 'No Title';
            const rate = (item.rate || "0.0").replace(/</g, '&lt;').replace(/>/g, '&gt;');
            const memo = (item.label || '').replace(/</g, '&lt;').replace(/>/g, '&gt;');
            const poster = item.cover || '';

            const card = document.createElement('div');
            card.className = 'media-card';
            card.style.width = '100%';
            card.style.margin = '0';
            card.innerHTML = `
                <a href="#" onclick="fillAndSearch('${title}'); return false;" title="${title}" style="display: block; width: 100%; text-decoration: none;">
                    <div class="media-poster" style="position: relative; aspect-ratio: 2/3; overflow: hidden;">
                        <img loading="lazy" src="${poster}" alt="${title}" referrerpolicy="no-referrer" style="width: 100%; height: 100%; object-fit: cover;">
                        ${rate !== '0.0' ? `<div class="media-badge" style="position: absolute; top: 0.5rem; right: 0.5rem; background-color: var(--accent); color: white; font-size: 0.75rem; font-weight: 700; padding: 0.25rem 0.5rem; border-radius: 0.25rem;">${rate}</div>` : ''}
                        ${memo ? `<div class="media-info" style="position: absolute; bottom: 0; left: 0; right: 0; background: linear-gradient(to top, rgba(0,0,0,0.8), transparent); padding: 1rem 0.5rem 0.5rem; color: white; font-size: 0.75rem;">${memo}</div>` : ''}
                    </div>
                    <div class="media-title" style="padding: 0.75rem; font-weight: 600; text-align: center; white-space: nowrap; overflow: hidden; text-overflow: ellipsis; color: var(--text-primary);">${title}</div>
                </a>
            `;

            resultsContainer.appendChild(card);
        });

        // 确保网格布局正确应用
        setTimeout(() => {
            if (typeof window.fixDoubanGrid === 'function') {
                window.fixDoubanGrid();
            }
        }, 100);
    }

    // 初始渲染
    renderTags();
    loadContent();

    // 标记为已初始化
    isInitialized = true;
}

// 填充搜索框并执行搜索
window.fillAndSearch = function(title) {
    if (!title) return;

    const input = document.getElementById('idx-search-input');
    if (input) {
        input.value = title;

        const form = input.closest('form');
        if (form) {
            form.submit();
        }
    }
};

// 当页面加载完成时，检查是否需要初始化豆瓣标签页
document.addEventListener('DOMContentLoaded', function() {
    // 检查是否在标签页界面
    const doubanTab = document.getElementById('douban-tab');
    if (!doubanTab) return;

    // 添加标签切换事件监听器
    const doubanButton = document.querySelector('.tab-button[data-tab="douban"]');
    if (doubanButton) {
        doubanButton.addEventListener('click', function() {
            setTimeout(initDoubanTab, 100);
        });
    }
});
