/* Enhanced Mobile Search Overlay Styles v2.0 */
.mobile-search-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: var(--bg-overlay);
    backdrop-filter: blur(8px);
    -webkit-backdrop-filter: blur(8px);
    z-index: 200;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    visibility: hidden;
    transition: all var(--duration-300) var(--transition-ease);
}

.mobile-search-overlay.active {
    opacity: 1;
    visibility: visible;
}

.mobile-search-container {
    width: 95%;
    max-width: 500px;
    background: var(--glass-bg);
    backdrop-filter: var(--glass-backdrop);
    -webkit-backdrop-filter: var(--glass-backdrop);
    border: 1px solid var(--glass-border);
    border-radius: var(--radius-2xl);
    overflow: hidden;
    box-shadow: var(--glass-shadow);
    transform: translateY(30px) scale(0.95);
    transition: all var(--duration-400) var(--transition-spring);
    display: flex;
    flex-direction: column;
    box-sizing: border-box;
}

.mobile-search-overlay.active .mobile-search-container {
    transform: translateY(0) scale(1);
}

.mobile-search-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: var(--spacing-4);
    border-bottom: 1px solid var(--border);
}

.mobile-search-header h3 {
    margin: 0;
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--text-primary);
}

.mobile-search-close {
    background: none;
    border: none;
    color: var(--text-tertiary);
    font-size: 1.8rem;
    cursor: pointer;
    padding: var(--spacing-1) var(--spacing-2);
    line-height: 1;
    transition: color 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.mobile-search-close:hover {
    color: var(--text-primary);
}

.mobile-search-form {
    padding: var(--spacing-4);
    width: 100%;
    box-sizing: border-box;
}

.mobile-search-form form {
    width: 100%;
    box-sizing: border-box;
}

.mobile-search-input-group {
    display: flex;
    position: relative;
    margin-bottom: var(--spacing-4);
    width: 100%;
    box-sizing: border-box;
}

.mobile-search-input {
    flex: 1;
    background-color: var(--bg-dark);
    color: var(--text-primary);
    border: 1px solid var(--border);
    border-radius: var(--radius-full);
    padding: var(--spacing-3) var(--spacing-4);
    padding-right: 50px;
    font-size: var(--font-size-base);
    transition: border-color 0.2s ease;
    width: 100%;
    box-sizing: border-box;
    min-width: 0; /* Prevent input from overflowing container */
}

.mobile-search-input:focus {
    outline: none;
    border-color: var(--primary);
}

.mobile-search-submit {
    position: absolute;
    right: 5px;
    top: 50%;
    transform: translateY(-50%);
    background-color: var(--primary);
    color: white;
    border: none;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.mobile-search-submit:hover {
    background-color: var(--primary-dark);
}

.mobile-search-options {
    display: flex;
    flex-wrap: wrap;
    gap: var(--spacing-3);
}

.mobile-search-option {
    display: flex;
    align-items: center;
    gap: var(--spacing-2);
    cursor: pointer;
    user-select: none;
}

.mobile-search-option input[type="radio"] {
    margin: 0;
}

.mobile-search-option span {
    font-size: var(--font-size-sm);
    color: var(--text-secondary);
}

/* Dark mode adjustments */
@media (prefers-color-scheme: dark) {
    .mobile-search-overlay {
        background-color: var(--background-dark, #111827);
    }

    .mobile-search-input {
        background-color: var(--input-background-dark, #1f2937);
        border-color: var(--border-dark, #374151);
        color: var(--text-primary-dark, #f3f4f6);
    }

    .mobile-search-input:focus {
        border-color: var(--primary-dark, #3b82f6);
        box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2);
    }
}

/* Responsive styles for smaller screens */
@media (max-width: 480px) {
    .mobile-search-container {
        width: 100%;
        max-width: 100%;
        border-radius: 0; /* Remove border radius on small screens for full width */
        margin: 0;
        height: 100%; /* Make it full height */
        transform: translateY(0); /* No initial transform */
    }

    .mobile-search-overlay {
        align-items: flex-start; /* Align to top */
    }

    .mobile-search-header {
        padding: var(--spacing-3);
    }

    .mobile-search-form {
        padding: var(--spacing-3);
        width: 100%;
    }

    .mobile-search-form form {
        width: 100%;
    }

    .mobile-search-input-group {
        width: 100%;
    }

    .mobile-search-input {
        padding: var(--spacing-2) var(--spacing-3);
        padding-right: 45px;
        width: 100%;
        font-size: 16px; /* Prevent iOS zoom on focus */
    }

    .mobile-search-submit {
        width: 36px;
        height: 36px;
        right: 4px;
    }

    .mobile-search-options {
        justify-content: space-between;
        width: 100%;
    }
}

/* Reduced motion preference */
@media (prefers-reduced-motion: reduce) {
    .mobile-search-overlay {
        transition: none;
    }

    .mobile-search-container {
        transition: none;
    }
}
