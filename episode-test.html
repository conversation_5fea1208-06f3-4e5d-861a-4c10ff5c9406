<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Episode List Test</title>

    <!-- Load CSS in the same order as base.html -->
    <link rel="stylesheet" href="/statics/css-variables.css">
    <link rel="stylesheet" href="/statics/critical.css">
    <link rel="stylesheet" href="/statics/enhanced-ui.css">
    <link rel="stylesheet" href="/statics/style.css">

    <style>
        body {
            padding: 20px;
            background-color: var(--bg-dark);
            color: var(--text-primary);
            font-family: var(--font-family);
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .test-section {
            margin-bottom: 40px;
            padding: 20px;
            background-color: var(--bg-card);
            border-radius: var(--radius-xl);
            border: 1px solid var(--border);
        }
        .test-title {
            font-size: var(--font-size-xl);
            font-weight: var(--font-weight-semibold);
            margin-bottom: 20px;
            color: var(--text-primary);
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Episode List Styling Test</h1>

        <div class="test-section">
            <div class="test-title">Enhanced Episode Tabs</div>

            <!-- Tab buttons -->
            <div class="tab-buttons">
                <button class="tab-button tabs-head-btn active tabs-curr" data-tab-id="source1">
                    <span>播放源1</span> <sup>24</sup>
                </button>
                <button class="tab-button tabs-head-btn" data-tab-id="source2">
                    <span>播放源2</span> <sup>12</sup>
                </button>
                <button class="tab-button tabs-head-btn" data-tab-id="source3">
                    <span>播放源3</span> <sup>36</sup>
                </button>
                <button class="tab-button tabs-head-btn" data-tab-id="detail">
                    <span>详情</span>
                </button>
            </div>

            <!-- Tab content -->
            <div class="tab-content-container">
                <div class="tab-content vod-episodes active tabs-curr" data-tab-id="source1">
                    <div class="episodes-grid">
                        <div class="episode-button vod-episode active vod-episode-curr">
                            <a href="#" class="vod-episode-play">第1集</a>
                        </div>
                        <div class="episode-button vod-episode">
                            <a href="#" class="vod-episode-play">第2集</a>
                        </div>
                        <div class="episode-button vod-episode">
                            <a href="#" class="vod-episode-play">第3集</a>
                        </div>
                        <div class="episode-button vod-episode">
                            <a href="#" class="vod-episode-play">第4集</a>
                        </div>
                        <div class="episode-button vod-episode">
                            <a href="#" class="vod-episode-play">第5集</a>
                        </div>
                        <div class="episode-button vod-episode">
                            <a href="#" class="vod-episode-play">第6集</a>
                        </div>
                        <div class="episode-button vod-episode">
                            <a href="#" class="vod-episode-play">第7集</a>
                        </div>
                        <div class="episode-button vod-episode">
                            <a href="#" class="vod-episode-play">第8集</a>
                        </div>
                        <div class="episode-button vod-episode">
                            <a href="#" class="vod-episode-play">第9集</a>
                        </div>
                        <div class="episode-button vod-episode">
                            <a href="#" class="vod-episode-play">第10集</a>
                        </div>
                        <div class="episode-button vod-episode">
                            <a href="#" class="vod-episode-play">第11集</a>
                        </div>
                        <div class="episode-button vod-episode">
                            <a href="#" class="vod-episode-play">第12集</a>
                        </div>
                        <div class="episode-button vod-episode">
                            <a href="#" class="vod-episode-play">第13集</a>
                        </div>
                        <div class="episode-button vod-episode">
                            <a href="#" class="vod-episode-play">第14集</a>
                        </div>
                        <div class="episode-button vod-episode">
                            <a href="#" class="vod-episode-play">第15集</a>
                        </div>
                        <div class="episode-button vod-episode">
                            <a href="#" class="vod-episode-play">第16集</a>
                        </div>
                        <div class="episode-button vod-episode">
                            <a href="#" class="vod-episode-play">第17集</a>
                        </div>
                        <div class="episode-button vod-episode">
                            <a href="#" class="vod-episode-play">第18集</a>
                        </div>
                        <div class="episode-button vod-episode">
                            <a href="#" class="vod-episode-play">第19集</a>
                        </div>
                        <div class="episode-button vod-episode">
                            <a href="#" class="vod-episode-play">第20集</a>
                        </div>
                        <div class="episode-button vod-episode">
                            <a href="#" class="vod-episode-play">第21集</a>
                        </div>
                        <div class="episode-button vod-episode">
                            <a href="#" class="vod-episode-play">第22集</a>
                        </div>
                        <div class="episode-button vod-episode">
                            <a href="#" class="vod-episode-play">第23集</a>
                        </div>
                        <div class="episode-button vod-episode">
                            <a href="#" class="vod-episode-play">第24集</a>
                        </div>
                    </div>
                </div>

                <div class="tab-content vod-episodes" data-tab-id="source2">
                    <div class="episodes-grid">
                        <div class="episode-button vod-episode">
                            <a href="#" class="vod-episode-play">EP01</a>
                        </div>
                        <div class="episode-button vod-episode">
                            <a href="#" class="vod-episode-play">EP02</a>
                        </div>
                        <div class="episode-button vod-episode">
                            <a href="#" class="vod-episode-play">EP03</a>
                        </div>
                        <div class="episode-button vod-episode">
                            <a href="#" class="vod-episode-play">EP04</a>
                        </div>
                        <div class="episode-button vod-episode">
                            <a href="#" class="vod-episode-play">EP05</a>
                        </div>
                        <div class="episode-button vod-episode">
                            <a href="#" class="vod-episode-play">EP06</a>
                        </div>
                        <div class="episode-button vod-episode">
                            <a href="#" class="vod-episode-play">EP07</a>
                        </div>
                        <div class="episode-button vod-episode">
                            <a href="#" class="vod-episode-play">EP08</a>
                        </div>
                        <div class="episode-button vod-episode">
                            <a href="#" class="vod-episode-play">EP09</a>
                        </div>
                        <div class="episode-button vod-episode">
                            <a href="#" class="vod-episode-play">EP10</a>
                        </div>
                        <div class="episode-button vod-episode">
                            <a href="#" class="vod-episode-play">EP11</a>
                        </div>
                        <div class="episode-button vod-episode">
                            <a href="#" class="vod-episode-play">EP12</a>
                        </div>
                    </div>
                </div>

                <div class="tab-content" data-tab-id="detail">
                    <div style="padding: 20px; background-color: var(--bg-dark); border-radius: var(--radius-lg);">
                        <h3>详情信息</h3>
                        <p>这里是详情内容...</p>
                    </div>
                </div>
            </div>
        </div>

        <div class="test-section">
            <div class="test-title">Scroll to Current Episode Test</div>
            <p>当前集数应该自动滚动到可见区域。点击下面的按钮测试滚动功能：</p>
            <div style="margin: 20px 0; display: flex; gap: 10px; flex-wrap: wrap;">
                <button class="btn btn-primary" onclick="scrollToCurrentEpisode()">滚动到当前集数</button>
                <button class="btn btn-secondary" onclick="scrollToEpisode('.episode-button:nth-child(15)')">滚动到第15集</button>
                <button class="btn btn-secondary" onclick="scrollToEpisode('.episode-button:last-child')">滚动到最后一集</button>
                <button class="btn btn-accent" onclick="testRandomEpisode()">随机选择并滚动</button>
            </div>
        </div>

        <div class="test-section">
            <div class="test-title">Mobile Responsive Test</div>
            <p>调整浏览器窗口大小来测试响应式设计。在移动设备上，集数按钮会变小，网格会调整为更适合的列数。</p>
        </div>
    </div>

    <!-- Load episode manager for scroll functionality -->
    <script src="/statics/vod/episode-manager.js"></script>

    <script>
        // Test functions for scroll functionality
        function testRandomEpisode() {
            const episodes = document.querySelectorAll('.episode-button');
            const randomIndex = Math.floor(Math.random() * episodes.length);
            const randomEpisode = episodes[randomIndex];

            // Remove active from all episodes
            episodes.forEach(ep => ep.classList.remove('active', 'vod-episode-curr'));

            // Add active to random episode
            randomEpisode.classList.add('active', 'vod-episode-curr');

            // Scroll to it
            setTimeout(() => {
                scrollToCurrentEpisode();
            }, 100);
        }

        // Simple tab functionality for testing
        document.addEventListener('DOMContentLoaded', function() {
            const tabButtons = document.querySelectorAll('.tab-button');
            const tabContents = document.querySelectorAll('.tab-content');

            tabButtons.forEach(button => {
                button.addEventListener('click', function() {
                    const tabId = this.getAttribute('data-tab-id');

                    // Remove active classes from all tabs
                    tabButtons.forEach(btn => {
                        btn.classList.remove('active', 'tabs-curr');
                    });
                    tabContents.forEach(content => {
                        content.classList.remove('active', 'tabs-curr');
                    });

                    // Add active classes to clicked tab and corresponding content
                    this.classList.add('active', 'tabs-curr');
                    const targetContent = document.querySelector(`[data-tab-id="${tabId}"]`);
                    if (targetContent && targetContent.classList.contains('tab-content')) {
                        targetContent.classList.add('active', 'tabs-curr');

                        // Scroll to active episode in the new tab
                        setTimeout(() => {
                            if (typeof scrollToCurrentEpisode === 'function') {
                                scrollToCurrentEpisode();
                            }
                        }, 150);
                    }
                });
            });

            // Add hover effects to episode buttons
            const episodeButtons = document.querySelectorAll('.episode-button');
            episodeButtons.forEach(button => {
                button.addEventListener('click', function(e) {
                    e.preventDefault();

                    // Remove active from all episodes in this grid
                    const grid = this.closest('.episodes-grid');
                    grid.querySelectorAll('.episode-button').forEach(btn => {
                        btn.classList.remove('active', 'vod-episode-curr');
                    });

                    // Add active to clicked episode
                    this.classList.add('active', 'vod-episode-curr');
                });
            });
        });
    </script>
</body>
</html>
