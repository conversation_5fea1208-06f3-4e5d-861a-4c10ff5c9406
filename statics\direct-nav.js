/**
 * Direct Navigation
 * Provides simplified, direct navigation functionality for mobile devices
 * without complex event handling or dependencies
 */

// Configuration
const CONFIG = {
    DESKTOP_BREAKPOINT: 768, // Width in pixels where desktop mode begins
    ANIMATION_DURATION: 300, // Duration for animations in ms
    SELECTORS: {
        BOTTOM_NAV: '.bottom-nav',
        NAV_BUTTONS: '.bottom-nav-btn',
        CATEGORIES: {
            BUTTON: '[data-action="categories"]',
            DROPDOWN: '#categories-dropdown',
            BACKDROP: '#categories-backdrop',
            BODY: '.categories-body',
            LIST: '.categories-list',
            SPACER: '.categories-spacer'
        },
        SEARCH: {
            BUTTON: '[data-action="search"]',
            OVERLAY: '.mobile-search-overlay',
            CONTAINER: '.mobile-search-container',
            INPUT: '.mobile-search-input'
        },
        HISTORY: {
            BUTTON: '[data-action="history"]',
            CONTAINER: '#history-dropdown',
            BACKDROP: '#history-backdrop',
            HEADER_BUTTON: '.history-button'
        },
        FAVORITES: {
            BUTTON: '[data-action="favorites"]',
            CONTAINER: '#favorites-dropdown',
            BACKDROP: '#favorites-backdrop',
            HEADER_BUTTON: '.favorites-button'
        }
    }
};

/**
 * Initialize direct navigation when DOM is loaded
 */
document.addEventListener('DOMContentLoaded', () => {
    setupDirectNavigation();
    checkBottomNavVisibility();
    setupHeaderButtons();
    window.addEventListener('resize', checkBottomNavVisibility);
});

/**
 * Check if bottom navigation should be visible based on screen width
 */
function checkBottomNavVisibility() {
    const bottomNav = document.querySelector(CONFIG.SELECTORS.BOTTOM_NAV);
    if (!bottomNav) return;

    const isDesktop = window.innerWidth > CONFIG.DESKTOP_BREAKPOINT;

    bottomNav.style.display = isDesktop ? 'none' : 'flex';
    bottomNav.style.visibility = isDesktop ? 'hidden' : 'visible';
}

/**
 * Set up direct navigation button handlers
 */
function setupDirectNavigation() {
    const { CATEGORIES, SEARCH, HISTORY, FAVORITES } = CONFIG.SELECTORS;

    // Get navigation buttons
    const categoryBtn = document.querySelector(`${CONFIG.SELECTORS.NAV_BUTTONS}${CATEGORIES.BUTTON}`);
    const searchBtn = document.querySelector(`${CONFIG.SELECTORS.NAV_BUTTONS}${SEARCH.BUTTON}`);
    const historyBtn = document.querySelector(`${CONFIG.SELECTORS.NAV_BUTTONS}${HISTORY.BUTTON}`);
    const favoritesBtn = document.querySelector(`${CONFIG.SELECTORS.NAV_BUTTONS}${FAVORITES.BUTTON}`);

    // Remove any existing onclick attributes for clean handling
    [categoryBtn, searchBtn, historyBtn, favoritesBtn].forEach(btn => {
        if (btn) btn.removeAttribute('onclick');
    });

    // Add direct click handlers
    if (categoryBtn) {
        categoryBtn.onclick = (e) => {
            e.preventDefault();
            directToggleCategories();
            return false;
        };
    }

    if (searchBtn) {
        searchBtn.onclick = (e) => {
            e.preventDefault();
            directOpenMobileSearch();
            return false;
        };
    }

    if (historyBtn) {
        historyBtn.onclick = (e) => {
            e.preventDefault();
            directToggleHistory();
            return false;
        };
    }

    if (favoritesBtn) {
        // Remove any existing event listeners
        favoritesBtn.onclick = null;
        favoritesBtn.removeEventListener('click', favoritesBtn._directHandler);

        // Add new event listener
        favoritesBtn._directHandler = (e) => {
            e.preventDefault();
            e.stopPropagation();
            console.log('Favorites button clicked - direct navigation');
            directToggleFavorites();
            return false;
        };

        favoritesBtn.addEventListener('click', favoritesBtn._directHandler, true);
        favoritesBtn.onclick = favoritesBtn._directHandler;
    }
}

/**
 * Toggle categories menu
 */
function directToggleCategories() {
    // Close other open menus first
    directCloseAll();

    const { CATEGORIES } = CONFIG.SELECTORS;
    const categoriesDropdown = document.querySelector(CATEGORIES.DROPDOWN);
    if (!categoriesDropdown) return;

    // Show categories
        categoriesDropdown.style.display = 'block';
        categoriesDropdown.style.visibility = 'visible';
        categoriesDropdown.style.opacity = '1';
        categoriesDropdown.style.zIndex = '1000';
        categoriesDropdown.style.transform = 'translateY(0)';
        categoriesDropdown.style.pointerEvents = 'auto';
        categoriesDropdown.classList.add('show');

    // Add bottom spacing on mobile
    if (window.innerWidth <= CONFIG.DESKTOP_BREAKPOINT) {
        adjustForBottomNav(categoriesDropdown);
    }

    // Show backdrop if available
    const categoriesBackdrop = document.querySelector(CATEGORIES.BACKDROP);
    if (categoriesBackdrop) {
        categoriesBackdrop.style.display = 'block';
        categoriesBackdrop.classList.add('show');
    }

    // Disable page scrolling
    disablePageScroll();

    // Set active button
    setActiveButton('categories');
}

/**
 * Adjust dropdown for bottom navigation
 * @param {HTMLElement} dropdown - The dropdown element to adjust
 */
function adjustForBottomNav(dropdown) {
    const { CATEGORIES } = CONFIG.SELECTORS;
    const categoriesBody = dropdown.querySelector(CATEGORIES.BODY);
    const categoriesList = dropdown.querySelector(CATEGORIES.LIST);
    const bottomNav = document.querySelector(CONFIG.SELECTORS.BOTTOM_NAV);

    if (!categoriesBody || !categoriesList || !bottomNav) return;

    const bottomNavHeight = bottomNav.offsetHeight;

    // Add padding to the dropdown
    dropdown.style.paddingBottom = `${bottomNavHeight}px`;

    // Adjust max-height for the body
    categoriesBody.style.maxHeight = `calc(100vh - ${bottomNavHeight + 70}px)`;
    categoriesBody.style.overflowY = 'auto';
    categoriesBody.style.webkitOverflowScrolling = 'touch';

    // Add spacer at the bottom if needed
    ensureSpacer(categoriesList, bottomNavHeight);
}

/**
 * Add or update spacer element for bottom navigation
 * @param {HTMLElement} list - The list element
 * @param {number} height - The height for the spacer
 */
function ensureSpacer(list, height) {
    const { CATEGORIES } = CONFIG.SELECTORS;
    let spacer = list.querySelector(CATEGORIES.SPACER);

    if (!spacer) {
        spacer = document.createElement('li');
        spacer.className = CATEGORIES.SPACER.substring(1); // Remove the dot
        spacer.style.cssText = `
            height: ${height}px;
            min-height: ${height}px;
            background: transparent;
            border: none;
            margin: 0;
            padding: 0;
        `;
        list.appendChild(spacer);
    } else {
        spacer.style.height = `${height}px`;
        spacer.style.minHeight = `${height}px`;
    }
}

/**
 * Open mobile search
 */
function directOpenMobileSearch() {
    // Close other open menus first
    directCloseAll();

    const { SEARCH } = CONFIG.SELECTORS;
    const searchOverlay = document.querySelector(SEARCH.OVERLAY);
    if (!searchOverlay) return;

    // Reset and show search overlay
        searchOverlay.style.transform = '';
        searchOverlay.style.opacity = '1';
    searchOverlay.style.display = 'flex';
        searchOverlay.style.visibility = 'visible';
        searchOverlay.style.zIndex = '1000';
        searchOverlay.style.pointerEvents = 'auto';
        searchOverlay.classList.add('active');

    // Adjust container for screen size
    const searchContainer = searchOverlay.querySelector(SEARCH.CONTAINER);
        if (searchContainer) {
            if (window.innerWidth <= 480) {
            // Very small screens
            searchContainer.style.cssText = `
                width: 100%;
                max-width: 100%;
                border-radius: 0;
                height: 100%;
                margin: 0;
            `;
                searchOverlay.style.alignItems = 'flex-start';
            } else {
            // Larger screens
            searchContainer.style.cssText = `
                width: 95%;
                max-width: 500px;
                border-radius: var(--radius-lg, 8px);
                margin: auto;
            `;
                searchOverlay.style.alignItems = 'center';
            }
    }

    // Auto-focus the search input
    setTimeout(() => {
        const searchInput = searchOverlay.querySelector(SEARCH.INPUT);
        if (searchInput) {
            searchInput.focus();
            searchInput.select();
        }
    }, 100);

    // Disable page scrolling
    disablePageScroll();

    // Set active button
    setActiveButton('search');
}

/**
 * Toggle browsing history panel
 */
function directToggleHistory() {
    // Close other open menus first
    directCloseAll();

    const { HISTORY } = CONFIG.SELECTORS;
    const historyContainer = document.querySelector(HISTORY.CONTAINER);
    const historyBackdrop = document.querySelector(HISTORY.BACKDROP);

    if (!historyContainer) return;

    // Show history container
    historyContainer.style.display = 'block';
    historyContainer.style.visibility = 'visible';
    historyContainer.style.opacity = '1';
    historyContainer.style.transform = 'translateY(0)';
    historyContainer.style.pointerEvents = 'auto';
    historyContainer.classList.add('show');
    historyContainer.classList.add('active');

    // Show backdrop
    if (historyBackdrop) {
        historyBackdrop.style.display = 'block';
        historyBackdrop.style.opacity = '1';
        historyBackdrop.style.visibility = 'visible';
        historyBackdrop.classList.add('show');

        // Add click event to backdrop
        historyBackdrop.onclick = directCloseAll;
    }

    // Activate header button if available
    const headerButton = document.querySelector(HISTORY.HEADER_BUTTON);
    if (headerButton) {
        headerButton.classList.add('active');
    }

    // Initialize history if needed
    if (typeof window.initHistory === 'function') {
        window.initHistory();
    }

    // Disable page scrolling
    disablePageScroll();

    // Set active button
    setActiveButton('history');
}

/**
 * Toggle favorites panel
 */
function directToggleFavorites() {
    console.log('directToggleFavorites called');
    const { FAVORITES } = CONFIG.SELECTORS;
    const favoritesContainer = document.querySelector(FAVORITES.CONTAINER);
    const favoritesBackdrop = document.querySelector(FAVORITES.BACKDROP);

    if (!favoritesContainer) {
        console.log('Favorites container not found');
        return;
    }

    // Check if favorites is already open
    const isOpen = favoritesContainer.classList.contains('show');
    console.log('Favorites is open:', isOpen);

    if (isOpen) {
        // Close favorites
        directCloseAll();
        return;
    }

    // Close other open menus first
    directCloseAll();

    console.log('Opening favorites...');

    // Reset any styles that might prevent the dropdown from showing
    favoritesContainer.style.display = 'block';
    favoritesContainer.style.visibility = 'visible';
    favoritesContainer.style.opacity = '1';
    favoritesContainer.style.transform = 'translateY(0)';
    favoritesContainer.style.pointerEvents = 'auto';
    favoritesContainer.style.zIndex = '1000';
    favoritesContainer.classList.add('show');
    favoritesContainer.classList.add('active');

    // Show backdrop
    if (favoritesBackdrop) {
        favoritesBackdrop.style.display = 'block';
        favoritesBackdrop.style.opacity = '1';
        favoritesBackdrop.style.visibility = 'visible';
        favoritesBackdrop.classList.add('show');

        // Add click event to backdrop
        favoritesBackdrop.onclick = directCloseAll;
    }

    // Activate header button if available
    const headerButton = document.querySelector(FAVORITES.HEADER_BUTTON);
    if (headerButton) {
        headerButton.classList.add('active');
    }

    // Load favorites items
    setTimeout(() => {
        if (typeof window.loadFavoritesItems === 'function') {
            window.loadFavoritesItems();
        }
    }, 100);

    // Initialize favorites if needed
    if (typeof window.initFavorites === 'function') {
        window.initFavorites();
    }

    // Disable page scrolling
    disablePageScroll();

    // Set active button
    setActiveButton('favorites');

    console.log('Favorites opened successfully');
}

/**
 * Close all open panels and overlays
 */
function directCloseAll() {
    const { CATEGORIES, SEARCH, HISTORY, FAVORITES } = CONFIG.SELECTORS;

    // Close categories
    const categoriesDropdown = document.querySelector(CATEGORIES.DROPDOWN);
    const categoriesBackdrop = document.querySelector(CATEGORIES.BACKDROP);

    if (categoriesDropdown) {
        categoriesDropdown.classList.remove('show');
        categoriesDropdown.classList.remove('active');
        categoriesDropdown.style.transform = 'translateY(100%)';
        categoriesDropdown.style.opacity = '0';
        categoriesDropdown.style.pointerEvents = 'none';

        setTimeout(() => {
            categoriesDropdown.style.visibility = 'hidden';
            categoriesDropdown.style.display = 'none';
        }, CONFIG.ANIMATION_DURATION);
    }

    if (categoriesBackdrop) {
        categoriesBackdrop.classList.remove('show');
        categoriesBackdrop.style.opacity = '0';
        categoriesBackdrop.style.visibility = 'hidden';

        setTimeout(() => {
            categoriesBackdrop.style.display = 'none';
        }, CONFIG.ANIMATION_DURATION);
    }

    // Close search
    const searchOverlay = document.querySelector(SEARCH.OVERLAY);
    if (searchOverlay) {
        searchOverlay.classList.remove('active');
        searchOverlay.style.opacity = '0';

        setTimeout(() => {
            searchOverlay.style.visibility = 'hidden';
            searchOverlay.style.display = 'none';
        }, CONFIG.ANIMATION_DURATION);
    }

    // Close history
    const historyContainer = document.querySelector(HISTORY.CONTAINER);
    const historyBackdrop = document.querySelector(HISTORY.BACKDROP);
    const historyHeaderButton = document.querySelector(HISTORY.HEADER_BUTTON);

    if (historyContainer) {
        historyContainer.classList.remove('active');
        historyContainer.classList.remove('show');
        historyContainer.style.transform = 'translateY(100%)';
        historyContainer.style.opacity = '0';
        historyContainer.style.pointerEvents = 'none';

        setTimeout(() => {
            historyContainer.style.visibility = 'hidden';
            historyContainer.style.display = 'none';
        }, CONFIG.ANIMATION_DURATION);
    }

    if (historyBackdrop) {
        historyBackdrop.classList.remove('show');
        historyBackdrop.style.opacity = '0';
        historyBackdrop.style.visibility = 'hidden';

        setTimeout(() => {
            historyBackdrop.style.display = 'none';
        }, CONFIG.ANIMATION_DURATION);
    }

    // Remove active state from history header button
    if (historyHeaderButton) {
        historyHeaderButton.classList.remove('active');
    }

    // Close favorites
    const favoritesContainer = document.querySelector(FAVORITES.CONTAINER);
    const favoritesBackdrop = document.querySelector(FAVORITES.BACKDROP);
    const favoritesHeaderButton = document.querySelector(FAVORITES.HEADER_BUTTON);

    if (favoritesContainer) {
        favoritesContainer.classList.remove('active');
        favoritesContainer.classList.remove('show');
        favoritesContainer.style.transform = 'translateY(100%)';
        favoritesContainer.style.opacity = '0';
        favoritesContainer.style.pointerEvents = 'none';

        setTimeout(() => {
            favoritesContainer.style.visibility = 'hidden';
            favoritesContainer.style.display = 'none';
            // Reset all inline styles to ensure clean state
            favoritesContainer.style.transform = '';
            favoritesContainer.style.opacity = '';
            favoritesContainer.style.pointerEvents = '';
            favoritesContainer.style.zIndex = '';
        }, CONFIG.ANIMATION_DURATION);
    }

    if (favoritesBackdrop) {
        favoritesBackdrop.classList.remove('show');
        favoritesBackdrop.style.opacity = '0';
        favoritesBackdrop.style.visibility = 'hidden';

        setTimeout(() => {
            favoritesBackdrop.style.display = 'none';
            // Reset backdrop styles
            favoritesBackdrop.style.opacity = '';
            favoritesBackdrop.style.visibility = '';
            // Remove any click handlers
            favoritesBackdrop.onclick = null;
        }, CONFIG.ANIMATION_DURATION);
    }

    // Remove active state from favorites header button
    if (favoritesHeaderButton) {
        favoritesHeaderButton.classList.remove('active');
    }

    // Re-enable page scrolling
    enablePageScroll();

    // Reset active buttons
    resetActiveButtons();
}

/**
 * Disable page scrolling
 */
function disablePageScroll() {
    const scrollY = window.scrollY;

    document.body.style.overflow = 'hidden';
    document.documentElement.style.overflow = 'hidden';
    document.body.style.position = 'fixed';
    document.body.style.width = '100%';
    document.body.style.height = '100%';
    document.body.style.top = `-${scrollY}px`;
    document.body.setAttribute('data-scroll-position', scrollY);
    }

/**
 * Re-enable page scrolling
 */
function enablePageScroll() {
    const scrollY = parseInt(document.body.getAttribute('data-scroll-position') || '0');

    document.body.style.overflow = '';
    document.documentElement.style.overflow = '';
    document.body.style.position = '';
    document.body.style.width = '';
    document.body.style.height = '';
    document.body.style.top = '';

    window.scrollTo(0, scrollY);
}

/**
 * Set active state for a button
 * @param {string} action - The action identifier
 */
function setActiveButton(action) {
    resetActiveButtons();

    const button = document.querySelector(`${CONFIG.SELECTORS.NAV_BUTTONS}[data-action="${action}"]`);
    if (button) {
        button.classList.add('active');
        button.setAttribute('aria-selected', 'true');
    }
}

/**
 * Reset active state for all buttons
 */
function resetActiveButtons() {
    const buttons = document.querySelectorAll(CONFIG.SELECTORS.NAV_BUTTONS);
    buttons.forEach(btn => {
        btn.classList.remove('active');
        btn.setAttribute('aria-selected', 'false');
    });
}

/**
 * Setup header buttons for mobile navigation
 */
function setupHeaderButtons() {
    const { HISTORY, FAVORITES } = CONFIG.SELECTORS;

    // Get header buttons
    const headerHistoryBtn = document.querySelector(HISTORY.HEADER_BUTTON);
    const headerFavoritesBtn = document.querySelector(FAVORITES.HEADER_BUTTON);

    // Add direct handlers to header buttons
    if (headerHistoryBtn) {
        headerHistoryBtn.addEventListener('click', function(e) {
            e.preventDefault();
            if (window.innerWidth <= CONFIG.DESKTOP_BREAKPOINT) {
                // On mobile, use direct navigation
                directToggleHistory();
            } else {
                // On desktop, use default toggle
                if (typeof window.toggleHistory === 'function') {
                    window.toggleHistory();
                }
            }
            return false;
        });
    }

    if (headerFavoritesBtn) {
        headerFavoritesBtn.addEventListener('click', function(e) {
            e.preventDefault();
            if (window.innerWidth <= CONFIG.DESKTOP_BREAKPOINT) {
                // On mobile, use direct navigation
                directToggleFavorites();
            } else {
                // On desktop, use default toggle
                if (typeof window.toggleFavorites === 'function') {
                    window.toggleFavorites();
                }
            }
            return false;
        });
    }
}

// Make essential functions globally available
window.directToggleCategories = directToggleCategories;
window.directOpenMobileSearch = directOpenMobileSearch;
window.directToggleHistory = directToggleHistory;
window.directToggleFavorites = directToggleFavorites;
window.directCloseAll = directCloseAll;
