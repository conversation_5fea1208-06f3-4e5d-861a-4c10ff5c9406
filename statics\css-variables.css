/* Enhanced CSS Variables - Modern Design System v2.0 */

:root {
  /* Modern Color Palette - Enhanced for better contrast and accessibility */
  --primary: #2563eb;
  --primary-dark: #1d4ed8;
  --primary-light: #3b82f6;
  --primary-50: #eff6ff;
  --primary-100: #dbeafe;
  --primary-200: #bfdbfe;
  --primary-300: #93c5fd;
  --primary-400: #60a5fa;
  --primary-500: #3b82f6;
  --primary-600: #2563eb;
  --primary-700: #1d4ed8;
  --primary-800: #1e40af;
  --primary-900: #1e3a8a;

  --secondary: #059669;
  --secondary-dark: #047857;
  --secondary-light: #10b981;
  --secondary-50: #ecfdf5;
  --secondary-100: #d1fae5;
  --secondary-500: #10b981;
  --secondary-600: #059669;
  --secondary-700: #047857;

  --accent: #f59e0b;
  --accent-dark: #d97706;
  --accent-light: #fbbf24;
  --accent-50: #fffbeb;
  --accent-100: #fef3c7;
  --accent-500: #f59e0b;
  --accent-600: #d97706;

  --success: #059669;
  --warning: #d97706;
  --error: #dc2626;
  --info: #2563eb;

  /* Enhanced Neutral Colors with better contrast */
  --bg-dark: #0f172a;
  --bg-darker: #020617;
  --bg-card: #1e293b;
  --bg-card-hover: #334155;
  --bg-card-active: #475569;
  --bg-overlay: rgba(15, 23, 42, 0.85);
  --bg-overlay-light: rgba(15, 23, 42, 0.6);
  --bg-glass: rgba(30, 41, 59, 0.8);

  --text-primary: #f8fafc;
  --text-secondary: #e2e8f0;
  --text-tertiary: #94a3b8;
  --text-muted: #64748b;
  --text-inverse: #0f172a;

  --border: #334155;
  --border-light: #475569;
  --border-lighter: #64748b;

  /* Enhanced Shadows & Effects */
  --shadow-xs: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  --shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-md: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-lg: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  --shadow-xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  --shadow-inner: inset 0 2px 4px 0 rgba(0, 0, 0, 0.06);
  --shadow-glow: 0 0 20px rgba(37, 99, 235, 0.15);
  --shadow-glow-lg: 0 0 40px rgba(37, 99, 235, 0.2);
  --shadow-colored: 0 10px 15px -3px rgba(37, 99, 235, 0.1), 0 4px 6px -2px rgba(37, 99, 235, 0.05);

  /* Modern Gradients */
  --gradient-primary: linear-gradient(135deg, var(--primary-600) 0%, var(--primary-700) 100%);
  --gradient-secondary: linear-gradient(135deg, var(--secondary-500) 0%, var(--secondary-600) 100%);
  --gradient-accent: linear-gradient(135deg, var(--accent-500) 0%, var(--accent-600) 100%);
  --gradient-dark: linear-gradient(135deg, var(--bg-card) 0%, var(--bg-card-hover) 100%);
  --gradient-glass: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
  --gradient-overlay: linear-gradient(180deg, transparent 0%, rgba(15, 23, 42, 0.8) 100%);

  /* Glass Morphism Effects */
  --glass-bg: rgba(30, 41, 59, 0.8);
  --glass-border: rgba(255, 255, 255, 0.1);
  --glass-backdrop: blur(12px);
  --glass-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);

  /* Enhanced Typography System */
  --font-family: 'Inter', 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Arial', sans-serif;
  --font-family-display: 'Inter', 'SF Pro Display', -apple-system, BlinkMacSystemFont, sans-serif;
  --font-family-mono: 'JetBrains Mono', 'Fira Code', 'SF Mono', 'Monaco', 'Consolas', 'Courier New', monospace;

  /* Font Sizes - Enhanced scale with better hierarchy */
  --font-size-xs: 0.75rem;      /* 12px */
  --font-size-sm: 0.875rem;     /* 14px */
  --font-size-base: 1rem;       /* 16px */
  --font-size-lg: 1.125rem;     /* 18px */
  --font-size-xl: 1.25rem;      /* 20px */
  --font-size-2xl: 1.5rem;      /* 24px */
  --font-size-3xl: 1.875rem;    /* 30px */
  --font-size-4xl: 2.25rem;     /* 36px */
  --font-size-5xl: 3rem;        /* 48px */
  --font-size-6xl: 3.75rem;     /* 60px */
  --font-size-7xl: 4.5rem;      /* 72px */
  --font-size-8xl: 6rem;        /* 96px */
  --font-size-9xl: 8rem;        /* 128px */

  /* Font Weights - Complete scale */
  --font-weight-thin: 100;
  --font-weight-extralight: 200;
  --font-weight-light: 300;
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;
  --font-weight-extrabold: 800;
  --font-weight-black: 900;

  /* Line Heights for better readability */
  --line-height-none: 1;
  --line-height-tight: 1.25;
  --line-height-snug: 1.375;
  --line-height-normal: 1.5;
  --line-height-relaxed: 1.625;
  --line-height-loose: 2;

  /* Enhanced Spacing Scale */
  --spacing-px: 1px;
  --spacing-0: 0;
  --spacing-0-5: 0.125rem;
  --spacing-1: 0.25rem;
  --spacing-1-5: 0.375rem;
  --spacing-2: 0.5rem;
  --spacing-2-5: 0.625rem;
  --spacing-3: 0.75rem;
  --spacing-3-5: 0.875rem;
  --spacing-4: 1rem;
  --spacing-5: 1.25rem;
  --spacing-6: 1.5rem;
  --spacing-7: 1.75rem;
  --spacing-8: 2rem;
  --spacing-9: 2.25rem;
  --spacing-10: 2.5rem;
  --spacing-11: 2.75rem;
  --spacing-12: 3rem;
  --spacing-14: 3.5rem;
  --spacing-16: 4rem;
  --spacing-20: 5rem;
  --spacing-24: 6rem;
  --spacing-28: 7rem;
  --spacing-32: 8rem;
  --spacing-36: 9rem;
  --spacing-40: 10rem;
  --spacing-44: 11rem;
  --spacing-48: 12rem;
  --spacing-52: 13rem;
  --spacing-56: 14rem;
  --spacing-60: 15rem;
  --spacing-64: 16rem;
  --spacing-72: 18rem;
  --spacing-80: 20rem;
  --spacing-96: 24rem;

  /* Enhanced Border Radius */
  --radius-none: 0;
  --radius-sm: 0.125rem;
  --radius: 0.25rem;
  --radius-md: 0.375rem;
  --radius-lg: 0.5rem;
  --radius-xl: 0.75rem;
  --radius-2xl: 1rem;
  --radius-3xl: 1.5rem;
  --radius-full: 9999px;

  /* Enhanced Transitions */
  --transition-none: none;
  --transition-all: all;
  --transition-fast: 75ms;
  --transition: 150ms;
  --transition-slow: 300ms;
  --transition-slower: 500ms;
  --transition-ease: cubic-bezier(0.4, 0, 0.2, 1);
  --transition-ease-in: cubic-bezier(0.4, 0, 1, 1);
  --transition-ease-out: cubic-bezier(0, 0, 0.2, 1);
  --transition-ease-in-out: cubic-bezier(0.4, 0, 0.2, 1);
  --transition-bounce: cubic-bezier(0.34, 1.56, 0.64, 1);
  --transition-spring: cubic-bezier(0.175, 0.885, 0.32, 1.275);

  /* Enhanced Z-index layers */
  --z-hide: -1;
  --z-auto: auto;
  --z-base: 0;
  --z-docked: 10;
  --z-dropdown: 1000;
  --z-sticky: 1100;
  --z-banner: 1200;
  --z-overlay: 1300;
  --z-modal: 1400;
  --z-popover: 1500;
  --z-skiplink: 1600;
  --z-toast: 1700;
  --z-tooltip: 1800;

  /* Breakpoints for responsive design */
  --breakpoint-sm: 640px;
  --breakpoint-md: 768px;
  --breakpoint-lg: 1024px;
  --breakpoint-xl: 1280px;
  --breakpoint-2xl: 1536px;

  /* Animation durations */
  --duration-75: 75ms;
  --duration-100: 100ms;
  --duration-150: 150ms;
  --duration-200: 200ms;
  --duration-300: 300ms;
  --duration-500: 500ms;
  --duration-700: 700ms;
  --duration-1000: 1000ms;

  /* Content sizing */
  --content-xs: 20rem;
  --content-sm: 24rem;
  --content-md: 28rem;
  --content-lg: 32rem;
  --content-xl: 36rem;
  --content-2xl: 42rem;
  --content-3xl: 48rem;
  --content-4xl: 56rem;
  --content-5xl: 64rem;
  --content-6xl: 72rem;
  --content-7xl: 80rem;

  /* Legacy compatibility variables */
  --main-accent: var(--accent);
  --main-accent-dark: var(--accent-dark);
  --main-accent-light: var(--accent-light);
  --main-card: var(--bg-card);
  --main-focus: var(--primary);
}