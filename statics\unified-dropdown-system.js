/**
 * Unified Dropdown System v1.0
 * A clean, consistent dropdown manager for all dropdowns
 */

class UnifiedDropdownManager {
    constructor() {
        this.activeDropdown = null;
        this.dropdowns = new Map();
        this.config = {
            animationDuration: 300,
            mobileBreakpoint: 768,
            zIndexBase: 1000
        };
        
        this.init();
    }

    init() {
        // Register all dropdowns
        this.registerDropdown('categories', {
            trigger: '.category-button, [data-action="categories"]',
            container: '#categories-dropdown',
            backdrop: '#categories-backdrop',
            type: 'fullscreen-mobile'
        });

        this.registerDropdown('history', {
            trigger: '.history-button, [data-action="history"]',
            container: '#history-dropdown',
            backdrop: '#history-backdrop',
            type: 'fullscreen-mobile',
            onOpen: () => this.loadHistoryItems()
        });

        this.registerDropdown('favorites', {
            trigger: '.favorites-button, [data-action="favorites"]',
            container: '#favorites-dropdown',
            backdrop: '#favorites-backdrop',
            type: 'fullscreen-mobile',
            onOpen: () => this.loadFavoritesItems()
        });

        this.registerDropdown('mobile-search', {
            trigger: '[data-action="search"]',
            container: '.mobile-search-overlay',
            backdrop: null,
            type: 'overlay',
            mobileOnly: true
        });

        // Set up global event listeners
        this.setupGlobalListeners();
    }

    registerDropdown(id, config) {
        this.dropdowns.set(id, {
            id,
            ...config,
            isOpen: false,
            elements: {
                triggers: document.querySelectorAll(config.trigger),
                container: document.querySelector(config.container),
                backdrop: config.backdrop ? document.querySelector(config.backdrop) : null
            }
        });

        // Add click listeners to triggers
        const dropdown = this.dropdowns.get(id);
        dropdown.elements.triggers.forEach(trigger => {
            trigger.addEventListener('click', (e) => {
                e.preventDefault();
                e.stopPropagation();
                this.toggle(id);
                return false;
            });
        });
    }

    toggle(dropdownId) {
        const dropdown = this.dropdowns.get(dropdownId);
        if (!dropdown || !dropdown.elements.container) return;

        // Check if mobile-only dropdown on desktop
        if (dropdown.mobileOnly && !this.isMobile()) {
            return;
        }

        if (dropdown.isOpen) {
            this.close(dropdownId);
        } else {
            this.open(dropdownId);
        }
    }

    open(dropdownId) {
        const dropdown = this.dropdowns.get(dropdownId);
        if (!dropdown || !dropdown.elements.container) return;

        // Close any currently open dropdown
        this.closeAll();

        // Set as active
        this.activeDropdown = dropdownId;
        dropdown.isOpen = true;

        const { container, backdrop } = dropdown.elements;

        // Reset any conflicting styles
        this.resetStyles(container);
        if (backdrop) this.resetStyles(backdrop);

        // Apply opening styles based on type
        if (dropdown.type === 'fullscreen-mobile' && this.isMobile()) {
            this.openFullscreenMobile(container, backdrop);
        } else if (dropdown.type === 'overlay') {
            this.openOverlay(container);
        } else {
            this.openDesktopDropdown(container, backdrop);
        }

        // Add classes
        container.classList.add('show', 'active');
        if (backdrop) backdrop.classList.add('show');

        // Set active button state
        this.setActiveButton(dropdownId);

        // Disable page scroll on mobile
        if (this.isMobile()) {
            this.disablePageScroll();
        }

        // Call onOpen callback
        if (dropdown.onOpen) {
            setTimeout(dropdown.onOpen, 50);
        }

        // Set up close handlers
        this.setupCloseHandlers(dropdownId);
    }

    close(dropdownId) {
        const dropdown = this.dropdowns.get(dropdownId);
        if (!dropdown || !dropdown.isOpen) return;

        const { container, backdrop } = dropdown.elements;

        // Remove classes
        container.classList.remove('show', 'active');
        if (backdrop) backdrop.classList.remove('show');

        // Apply closing animation
        container.style.opacity = '0';
        container.style.transform = this.isMobile() ? 'translateY(100%)' : 'translateY(-10px)';
        container.style.pointerEvents = 'none';

        if (backdrop) {
            backdrop.style.opacity = '0';
            backdrop.style.visibility = 'hidden';
        }

        // Clean up after animation
        setTimeout(() => {
            if (!dropdown.isOpen) { // Only if still closed
                container.style.display = 'none';
                container.style.visibility = 'hidden';
                this.resetStyles(container);
                if (backdrop) {
                    backdrop.style.display = 'none';
                    this.resetStyles(backdrop);
                }
            }
        }, this.config.animationDuration);

        // Update state
        dropdown.isOpen = false;
        if (this.activeDropdown === dropdownId) {
            this.activeDropdown = null;
        }

        // Reset button states
        this.resetActiveButtons();

        // Re-enable page scroll
        this.enablePageScroll();

        // Remove close handlers
        this.removeCloseHandlers();
    }

    closeAll() {
        this.dropdowns.forEach((dropdown, id) => {
            if (dropdown.isOpen) {
                this.close(id);
            }
        });
    }

    openFullscreenMobile(container, backdrop) {
        container.style.display = 'block';
        container.style.visibility = 'visible';
        container.style.position = 'fixed';
        container.style.top = '0';
        container.style.left = '0';
        container.style.right = '0';
        container.style.bottom = '0';
        container.style.zIndex = this.config.zIndexBase;
        container.style.opacity = '1';
        container.style.transform = 'translateY(0)';
        container.style.pointerEvents = 'auto';

        if (backdrop) {
            backdrop.style.display = 'block';
            backdrop.style.visibility = 'visible';
            backdrop.style.opacity = '1';
        }
    }

    openOverlay(container) {
        container.style.display = 'flex';
        container.style.visibility = 'visible';
        container.style.opacity = '1';
        container.style.zIndex = this.config.zIndexBase + 10;
        container.classList.add('active');
    }

    openDesktopDropdown(container, backdrop) {
        container.style.display = 'block';
        container.style.visibility = 'visible';
        container.style.opacity = '1';
        container.style.zIndex = this.config.zIndexBase;
        container.style.transform = 'translateY(0)';
        container.style.pointerEvents = 'auto';

        if (backdrop) {
            backdrop.style.display = 'block';
            backdrop.style.visibility = 'visible';
            backdrop.style.opacity = '1';
        }
    }

    resetStyles(element) {
        const stylesToReset = [
            'display', 'visibility', 'opacity', 'transform', 'pointerEvents',
            'zIndex', 'position', 'top', 'left', 'right', 'bottom'
        ];
        
        stylesToReset.forEach(prop => {
            element.style[prop] = '';
        });
    }

    setupCloseHandlers(dropdownId) {
        const dropdown = this.dropdowns.get(dropdownId);
        
        // Backdrop click
        if (dropdown.elements.backdrop) {
            dropdown.elements.backdrop.onclick = () => this.close(dropdownId);
        }

        // Click outside (desktop only)
        if (!this.isMobile()) {
            this.outsideClickHandler = (e) => {
                if (!dropdown.elements.container.contains(e.target) &&
                    !Array.from(dropdown.elements.triggers).some(trigger => trigger.contains(e.target))) {
                    this.close(dropdownId);
                }
            };
            setTimeout(() => {
                document.addEventListener('click', this.outsideClickHandler);
            }, 10);
        }
    }

    removeCloseHandlers() {
        if (this.outsideClickHandler) {
            document.removeEventListener('click', this.outsideClickHandler);
            this.outsideClickHandler = null;
        }
    }

    setupGlobalListeners() {
        // Escape key
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && this.activeDropdown) {
                this.close(this.activeDropdown);
            }
        });

        // Window resize
        window.addEventListener('resize', () => {
            if (this.activeDropdown) {
                // Close all dropdowns on resize to prevent layout issues
                this.closeAll();
            }
        });
    }

    setActiveButton(dropdownId) {
        this.resetActiveButtons();
        const dropdown = this.dropdowns.get(dropdownId);
        dropdown.elements.triggers.forEach(trigger => {
            trigger.classList.add('active');
        });
    }

    resetActiveButtons() {
        document.querySelectorAll('.bottom-nav-btn, .category-button, .history-button, .favorites-button').forEach(btn => {
            btn.classList.remove('active');
        });
    }

    disablePageScroll() {
        const scrollY = window.scrollY;
        document.body.style.overflow = 'hidden';
        document.body.style.position = 'fixed';
        document.body.style.width = '100%';
        document.body.style.top = `-${scrollY}px`;
        document.body.setAttribute('data-scroll-position', scrollY);
    }

    enablePageScroll() {
        const scrollY = parseInt(document.body.getAttribute('data-scroll-position') || '0');
        document.body.style.overflow = '';
        document.body.style.position = '';
        document.body.style.width = '';
        document.body.style.top = '';
        window.scrollTo(0, scrollY);
    }

    isMobile() {
        return window.innerWidth <= this.config.mobileBreakpoint;
    }

    // Helper methods for loading content
    loadHistoryItems() {
        if (typeof window.loadHistoryItems === 'function') {
            window.loadHistoryItems();
        }
    }

    loadFavoritesItems() {
        if (typeof window.loadFavoritesItems === 'function') {
            window.loadFavoritesItems();
        }
    }
}

// Initialize the unified dropdown system
let dropdownManager;

document.addEventListener('DOMContentLoaded', () => {
    dropdownManager = new UnifiedDropdownManager();
    
    // Make it globally available
    window.dropdownManager = dropdownManager;
    
    // Provide backward compatibility functions
    window.directToggleCategories = () => dropdownManager.toggle('categories');
    window.directToggleHistory = () => dropdownManager.toggle('history');
    window.directToggleFavorites = () => dropdownManager.toggle('favorites');
    window.directOpenMobileSearch = () => dropdownManager.toggle('mobile-search');
    window.directCloseAll = () => dropdownManager.closeAll();
    
    // Desktop compatibility
    window.toggleCategories = () => dropdownManager.toggle('categories');
    window.toggleHistory = () => dropdownManager.toggle('history');
    window.toggleFavorites = () => dropdownManager.toggle('favorites');
});
