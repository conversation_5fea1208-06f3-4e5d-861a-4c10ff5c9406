<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CSS Debug Test</title>
    
    <!-- Load CSS in the same order as base.html -->
    <link rel="stylesheet" href="/statics/css-variables.css">
    <link rel="stylesheet" href="/statics/critical.css">
    <link rel="stylesheet" href="/statics/enhanced-ui.css">
    <link rel="stylesheet" href="/statics/style.css">
    
    <style>
        body {
            padding: 20px;
            font-family: Arial, sans-serif;
        }
        .debug-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ccc;
            border-radius: 8px;
        }
        .debug-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 15px;
            color: #333;
        }
        .css-test {
            margin: 10px 0;
            padding: 10px;
            background: #f5f5f5;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <h1>CSS Debug Test Page</h1>
    
    <div class="debug-section">
        <div class="debug-title">CSS Variables Test</div>
        <div class="css-test" style="color: var(--text-primary, red); background-color: var(--bg-card, yellow);">
            If CSS variables are loaded, this text should be light colored on a dark background.
            If not, it will be red text on yellow background.
        </div>
    </div>
    
    <div class="debug-section">
        <div class="debug-title">Enhanced UI Components Test</div>
        
        <div class="css-test">
            <button class="btn btn-primary">Primary Button</button>
            <button class="btn btn-secondary">Secondary Button</button>
            <button class="btn btn-accent">Accent Button</button>
        </div>
        
        <div class="css-test">
            <div class="search-form">
                <select class="search-select">
                    <option>Search Type</option>
                </select>
                <input class="search-input" placeholder="Search input">
                <button class="search-button">Search</button>
            </div>
        </div>
    </div>
    
    <div class="debug-section">
        <div class="debug-title">Media Cards Test</div>
        <div class="card-grid">
            <div class="media-card">
                <div class="media-poster">
                    <img src="https://via.placeholder.com/200x300/333/fff?text=Test+Image" alt="Test">
                    <div class="media-badge">8.5</div>
                    <div class="media-info">Test episode info</div>
                </div>
                <div class="media-title">Test Movie Title</div>
            </div>
            
            <div class="media-card">
                <div class="media-poster">
                    <img src="https://via.placeholder.com/200x300/666/fff?text=Test+2" alt="Test 2">
                    <div class="media-badge">9.0</div>
                    <div class="media-info">Another test</div>
                </div>
                <div class="media-title">Another Test Movie</div>
            </div>
            
            <div class="media-card">
                <div class="media-poster">
                    <img src="https://via.placeholder.com/200x300/999/fff?text=Test+3" alt="Test 3">
                    <div class="media-badge">7.8</div>
                    <div class="media-info">Third test</div>
                </div>
                <div class="media-title">Third Test Movie Title</div>
            </div>
        </div>
    </div>
    
    <div class="debug-section">
        <div class="debug-title">Legacy Compatibility Test</div>
        <div class="vod-list card-grid">
            <div class="vod-item media-card">
                <a href="#">
                    <div class="vod-poster media-poster">
                        <img src="https://via.placeholder.com/200x300/c33/fff?text=Legacy+Test" alt="Legacy Test">
                        <div class="vod-badge media-badge">8.0</div>
                        <div class="vod-memo media-info">Legacy episode info</div>
                    </div>
                    <div class="vod-title media-title">Legacy Test Movie</div>
                </a>
            </div>
        </div>
    </div>
    
    <script>
        // Debug script to check CSS loading
        document.addEventListener('DOMContentLoaded', function() {
            console.log('=== CSS Debug Information ===');
            
            // Check if CSS variables are available
            const testElement = document.createElement('div');
            document.body.appendChild(testElement);
            testElement.style.color = 'var(--text-primary)';
            const computedColor = getComputedStyle(testElement).color;
            console.log('CSS Variables loaded:', computedColor !== 'var(--text-primary)');
            console.log('Computed text color:', computedColor);
            document.body.removeChild(testElement);
            
            // Check if enhanced UI classes are applied
            const btnPrimary = document.querySelector('.btn-primary');
            if (btnPrimary) {
                const btnStyles = getComputedStyle(btnPrimary);
                console.log('Button primary background:', btnStyles.backgroundColor);
                console.log('Button border radius:', btnStyles.borderRadius);
            }
            
            // Check media card styles
            const mediaCard = document.querySelector('.media-card');
            if (mediaCard) {
                const cardStyles = getComputedStyle(mediaCard);
                console.log('Media card display:', cardStyles.display);
                console.log('Media card border radius:', cardStyles.borderRadius);
                console.log('Media card background:', cardStyles.backgroundColor);
            }
            
            // Check grid layout
            const cardGrid = document.querySelector('.card-grid');
            if (cardGrid) {
                const gridStyles = getComputedStyle(cardGrid);
                console.log('Card grid display:', gridStyles.display);
                console.log('Card grid template columns:', gridStyles.gridTemplateColumns);
                console.log('Card grid gap:', gridStyles.gap);
            }
            
            console.log('=== End CSS Debug ===');
        });
    </script>
</body>
</html>
