/**
 * Dropdown System Override
 * Disables old dropdown functions and ensures unified system takes precedence
 */

document.addEventListener('DOMContentLoaded', () => {
    // Wait for unified dropdown system to load
    setTimeout(() => {
        if (window.dropdownManager) {
            console.log('Unified dropdown system loaded, overriding old functions...');
            
            // Override all old dropdown functions with unified system calls
            window.toggleCategories = () => window.dropdownManager.toggle('categories');
            window.openCategories = () => window.dropdownManager.open('categories');
            window.closeCategories = () => window.dropdownManager.close('categories');
            
            window.toggleHistory = () => window.dropdownManager.toggle('history');
            window.openHistory = () => window.dropdownManager.open('history');
            window.closeHistory = () => window.dropdownManager.close('history');
            
            window.toggleFavorites = () => window.dropdownManager.toggle('favorites');
            window.openFavorites = () => window.dropdownManager.open('favorites');
            window.closeFavorites = () => window.dropdownManager.close('favorites');
            
            // Direct navigation functions (for mobile)
            window.directToggleCategories = () => window.dropdownManager.toggle('categories');
            window.directToggleHistory = () => window.dropdownManager.toggle('history');
            window.directToggleFavorites = () => window.dropdownManager.toggle('favorites');
            window.directOpenMobileSearch = () => window.dropdownManager.toggle('mobile-search');
            window.directCloseAll = () => window.dropdownManager.closeAll();
            
            // Remove old event listeners by cloning and replacing elements
            const removeOldEventListeners = () => {
                // Categories button
                const categoryBtns = document.querySelectorAll('.category-button, [data-action="categories"]');
                categoryBtns.forEach(btn => {
                    const newBtn = btn.cloneNode(true);
                    btn.parentNode.replaceChild(newBtn, btn);
                });
                
                // History button
                const historyBtns = document.querySelectorAll('.history-button, [data-action="history"]');
                historyBtns.forEach(btn => {
                    const newBtn = btn.cloneNode(true);
                    btn.parentNode.replaceChild(newBtn, btn);
                });
                
                // Favorites button
                const favoritesBtns = document.querySelectorAll('.favorites-button, [data-action="favorites"]');
                favoritesBtns.forEach(btn => {
                    const newBtn = btn.cloneNode(true);
                    btn.parentNode.replaceChild(newBtn, btn);
                });
                
                // Search button
                const searchBtns = document.querySelectorAll('[data-action="search"]');
                searchBtns.forEach(btn => {
                    const newBtn = btn.cloneNode(true);
                    btn.parentNode.replaceChild(newBtn, btn);
                });
            };
            
            // Clean up old event listeners
            removeOldEventListeners();
            
            // Re-register with unified system
            window.dropdownManager.init();
            
            console.log('Dropdown system override complete');
        } else {
            console.warn('Unified dropdown system not found, retrying...');
            // Retry after another delay
            setTimeout(arguments.callee, 500);
        }
    }, 100);
});

// Disable old direct-nav.js functions if they exist
if (window.directCloseAll) {
    const originalDirectCloseAll = window.directCloseAll;
    window.directCloseAll = function() {
        if (window.dropdownManager) {
            window.dropdownManager.closeAll();
        } else {
            originalDirectCloseAll();
        }
    };
}

// Override category-config.js functions
document.addEventListener('DOMContentLoaded', () => {
    // Disable category-config.js overrides
    setTimeout(() => {
        if (window.dropdownManager) {
            // Ensure categories use unified system
            window.toggleCategories = () => window.dropdownManager.toggle('categories');
            window.directToggleCategories = () => window.dropdownManager.toggle('categories');
        }
    }, 200);
});
